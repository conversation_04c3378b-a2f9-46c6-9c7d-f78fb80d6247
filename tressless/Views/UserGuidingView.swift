//
//  UserGuidingView.swift
//  tressless
//
//  Created by <PERSON> on 28.08.2024.
//

import SwiftUI
import Firebase

struct UserGuidingView: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger
    @Binding var selectedTab: Tab
    @Binding var showUserGuiding: Bool
    @State private var currentStep = 0
    @State private var hasImages = false
    @State private var hasPath = false
    @State private var hasAnalysis = false
    @State private var showClassifyView = false
    @State private var showPathView = false
    @State private var showGeminiView = false
    @State private var analysisCompleted = false
    @State private var pathEnrolled = false
    @Environment(\.dismiss) private var dismiss
    
    @StateObject private var medicationManager = MedicationManager()
    @State private var medications: [MedicationDocument] = []

    @State private var isAnimating = false

    var body: some View {
        VStack(spacing: 20) {
            VStack {
                Text("Welcome to the Snap Hair Analyze!")
                .foregroundColor(.black)
                    .font(.custom("KdamThmorPro-Regular", size: 28))
                    .padding()

                Text("Step \(currentStep + 1) of 3")
                    .font(.headline)
                    .foregroundColor(Color("Background 2"))
            }
            .padding()
            .background(Color(UIColor(hexString: "#DDE6CC")))
            .cornerRadius(10)

            VStack(spacing: 16) {
                switch currentStep {
                case 0:
                    StepButton(title: "Do Hair Analysis", action: { showClassifyView = true })
                case 1:
                    StepButton(title: "Enroll Path", action: { showPathView = true })
                case 2:
                    StepButton(title: "Upload Images", action: { showGeminiView = true })
                default:
                    EmptyView()
                }
            }
            .padding()
            .background(Color("Background"))
            .cornerRadius(10)

            Text("Complete these steps to get started with your hair growth journey.")
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding()
        }
        .padding()
        .background(Color("Background"))
        .onAppear(perform: checkUserProgress)
        .onChange(of: hasImages) { _ in updateStep() }
        .onChange(of: hasPath) { _ in updateStep() }
        .onChange(of: hasAnalysis) { _ in updateStep() }
        .sheet(isPresented: $showClassifyView) {
            NavigationView {
                ClassifyView(onCompletion: { success in
                    if success {
                        analysisCompleted = true
                        checkUserProgress()
                    }
                })
                .navigationBarItems(trailing: Group {
                    if analysisCompleted {
                        Button("Complete Step") {
                            showClassifyView = false
                        }
                        .buttonStyle(PrimaryButtonStyle())
                        .offset(x: isAnimating ? -5 : 5)
                        .animation(Animation.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isAnimating)
                        .onAppear { isAnimating = true }
                    }
                })
            }
        }
        .onChange(of: showClassifyView) { newValue in
            if !newValue {
                checkUserProgress()
            }
        }
        .sheet(isPresented: $showPathView) {
            NavigationView {
                PathView(medications: $medications, selectedTab: $selectedTab, medicationManager: medicationManager, onPathEnrolled: { success in
                    if success {
                        pathEnrolled = true
                        checkUserProgress()
                    }
                })
                .navigationBarItems(trailing: Group {
                    if pathEnrolled {
                        Button("Complete Step") {
                            showPathView = false
                        }
                        .buttonStyle(PrimaryButtonStyle())
                        .offset(x: isAnimating ? -5 : 5)
                        .animation(Animation.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isAnimating)
                        .onAppear { isAnimating = true }
                    }
                })
            }
        }
        .onChange(of: showPathView) { newValue in
            if !newValue {
                checkUserProgress()
            }
        }
        .sheet(isPresented: $showGeminiView) {
          NavigationView {
            GeminiView(onCompletion: {
              showGeminiView = false
              checkUserProgress()
            })
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
              ToolbarItem(placement: .principal) {
                Text("Upload just one image for now")
                  .font(.headline)
                  .foregroundColor(.black)
                  .opacity(isAnimating ? 1 : 0.3)
                  .animation(Animation.easeInOut(duration: 1).repeatForever(autoreverses: true), value: isAnimating)
                  .onAppear { isAnimating = true }
              }
            }
          }
        }
        .onChange(of: showGeminiView) { newValue in
            if !newValue {
                checkUserProgress()
            }
        }
    }

    private func checkUserProgress() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        let db = Firestore.firestore()

        let dispatchGroup = DispatchGroup()

        dispatchGroup.enter()
        db.collection("users").document(userId).collection("images").getDocuments { (snapshot, error) in
            hasImages = snapshot?.isEmpty == false
            dispatchGroup.leave()
        }

        dispatchGroup.enter()
        db.collection("users").document(userId).collection("paths").getDocuments { (snapshot, error) in
            hasPath = snapshot?.isEmpty == false
            dispatchGroup.leave()
        }

        dispatchGroup.enter()
        db.collection("users").document(userId).collection("hair_density_analyses").getDocuments { (snapshot, error) in
            hasAnalysis = snapshot?.isEmpty == false
            dispatchGroup.leave()
        }

        dispatchGroup.notify(queue: .main) {
            updateStep()
            analysisCompleted = hasAnalysis
            pathEnrolled = hasPath
        }
    }

    private func updateStep() {
        if !hasAnalysis {
            currentStep = 0
        } else if !hasPath {
            currentStep = 1
        } else if !hasImages {
            currentStep = 2
        } else {
            completeUserGuiding()
        }
        
        AnalyticsInfoLogger.shared.logEvent("User_Guiding_Step_Changed", properties: ["step": currentStep + 1])
    }

    private func completeUserGuiding() {
        showUserGuiding = false
        selectedTab = .chat // Set the tab to show HomeView
        dismiss() // Dismiss the UserGuidingView
    }
}

struct StepButton: View {
    let title: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: "arrow.right.circle.fill")
                    .foregroundColor(.white)
                Text(title)
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color("Background 2"))
            .foregroundColor(.white)
            .cornerRadius(10)
        }
    }
}

struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding()
            .background(Color("Background 4"))
            .foregroundColor(Color("Background"))
            .cornerRadius(10)
    }
}

// Add the preview struct
#Preview {
  UserGuidingView(selectedTab: .constant(.chat), showUserGuiding: .constant(true))
}

