//
//  GeminiView.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI
import Foundation
import SwiftUI
import FirebaseStorage
import FirebaseFirestore
import FirebaseAuth
import Kingfisher
import PhotosUI

struct ImageData2: Identifiable, Decodable, Equatable {
    let id: String
    let url: String
    let area: Area2
    let date: Date

    enum CodingKeys: String, CodingKey {
        case id, url, area, date
    }

    init(id: String, url: String, area: Area2, date: Date) {
        self.id = id
        self.url = url
        self.area = area
        self.date = date
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        url = try container.decode(String.self, forKey: .url)
        area = try container.decode(Area2.self, forKey: .area)
        date = try container.decode(Date.self, forKey: .date)
    }

    var dictionary: [String: Any] {
        return [
            "id": id,
            "url": url,
            "area": area.rawValue,
            "date": Timestamp(date: date)
        ]
    }

    static func == (lhs: ImageData2, rhs: ImageData2) -> Bool {
        return lhs.id == rhs.id && lhs.url == rhs.url && lhs.area == rhs.area && lhs.date == rhs.date
    }
}
enum Area2: String, CaseIterable, Identifiable, Decodable {
    case front = "Front"
    case back = "Back"
    case top = "Top"
    case right = "Right"
    case left = "Left"
    
    var id: String { self.rawValue }
    
    init?(rawValue: String) {
        switch rawValue {
        case "Front":
            self = .front
        case "Back":
            self = .back
        case "Top":
            self = .top
        case "Right":
            self = .right
        case "Left":
            self = .left
        default:
            return nil
        }
    }
}
class AreaManager: ObservableObject {
    @Published var selectedArea: Area2 = .front
    @Published var needsRefresh: Bool = false // Add a refresh flag

}

struct GeminiView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger

    @StateObject var areaManager = AreaManager()
    @State private var images: [ImageData2] = [] // Hold the image data here
    @State private var currentIndex: Double = 0
    @State private var sortedImages: [ImageData2] = []
    
    
    
    var onCompletion: () -> Void
    
    init(onCompletion: @escaping () -> Void) {
        self.onCompletion = onCompletion
    }
    
    var body: some View {
        VStack {
            VStack(alignment: /*@START_MENU_TOKEN@*/.center/*@END_MENU_TOKEN@*/)  {
                
                HStack(alignment: /*@START_MENU_TOKEN@*/.center/*@END_MENU_TOKEN@*/) {
                    Text("Follow your progress")
                        .font(.custom("KdamThmorPro-Regular", size: 20))
                        .fontWeight(.bold)
                    
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .center) // Add this line
                
                
                
                
                
                
                CustomPicker(items: Area2.allCases, selection: $areaManager.selectedArea) { area in
                    VStack {
                        Image(area.rawValue)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(height: 40)
                        Text(area.rawValue)
                            .font(.footnote)
                    }
                    
                }
                .padding()
                
            }
            .background(Color(UIColor(hexString: "#DDE6CC")))
            
            ImageUploadView(selectedArea: $areaManager.selectedArea, images: $images, areaManager: areaManager, onCompletion: onCompletion)
            DirectImageSwitcherView(selectedArea: areaManager.selectedArea, areaManager: areaManager, images: $sortedImages, currentIndex: $currentIndex)
            
            
        }
        
        .onAppear {
          AnalyticsInfoLogger.shared.logEvent("Progress_View_Opened")

            fetchImages(area: areaManager.selectedArea) // Pass selectedArea to fetchImages
        }
        .onChange(of: areaManager.selectedArea) { newArea in
          AnalyticsInfoLogger.shared.logEvent("Area_Changed", properties: ["area": newArea.rawValue])

            fetchImages(area: newArea) // Fetch images when selectedArea changes
            currentIndex = 0}
        .onChange(of: images) { newImages in
            sortedImages = newImages.sorted { $0.date < $1.date }
            if !newImages.isEmpty {
                onCompletion()
            }
        }
        .background(Color("Background"))
    }
      

    
    func fetchImages(area: Area2) { // Modify fetchImages to take area as a parameter
        guard let userId = Auth.auth().currentUser?.uid else { return }
        let db = Firestore.firestore()
        db.collection("users").document(userId).collection("images")
            .whereField("area", isEqualTo: area.rawValue) // Use the passed area parameter
                 .getDocuments { snapshot, error in

            if let error = error {
                print("Error fetching images: \(error.localizedDescription)") // Log the error
                return
            }

            guard let documents = snapshot?.documents else {
                print("No documents found") // Log if no documents
                return
            }

            self.images = documents.compactMap { document in
                do {

                    return try document.data(as: ImageData2.self)
                } catch {
                    print("Error decoding image data: \(error.localizedDescription)") // Log decoding errors
                    return nil
                }
            }
        }
      AnalyticsInfoLogger.shared.logEvent("Images_Fetched", properties: ["area": area.rawValue, "count": images.count])

    }
}


struct ImageUploadView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger

    @Binding var selectedArea: Area2
    @Binding var images: [ImageData2] // Add images binding

    @State private var selectedItem: PhotosPickerItem?
    @State private var selectedImage: UIImage?
    @State private var selectedDate = Date() // Add a state variable for the selected date
     @State private var showDatePicker = false // Add a state variable to control the date picker visibility
    @State private var showDatePickerSheet = false // State variable for showing the date picker sheet

    @State private var uploadProgress: Double = 0
    @ObservedObject var areaManager: AreaManager // Observe the AreaManager
    @State private var showPicker = false

    private let storageRef = Storage.storage().reference()
    @Environment(\.dismiss) private var dismiss

    var onCompletion: () -> Void

    func uploadImage(item: PhotosPickerItem, area: Area2) async throws {
        guard let userId = Auth.auth().currentUser?.uid else { return }

        // Load image data from PhotosPickerItem
        guard let imageData = try await item.loadTransferable(type: Data.self),
              let uiImage = UIImage(data: imageData) else { return }

        // Compress the image
        let compressionQuality: CGFloat = 0.5 // Adjust this value to balance quality and file size
        guard let compressedImageData = uiImage.jpegData(compressionQuality: compressionQuality) else {
            print("Failed to compress image")
            return
        }

        // Create a reference for the image with a unique name
        let imageName = UUID().uuidString + ".jpg"
        let imageRef = storageRef.child("images/\(area.rawValue)/\(imageName)")

        // Upload the compressed image
        let uploadTask = imageRef.putData(compressedImageData)

        // Observe progress and completion
        uploadTask.observe(.progress) { snapshot in
            self.uploadProgress = Double(snapshot.progress!.completedUnitCount) / Double(snapshot.progress!.totalUnitCount)
          
        }

        uploadTask.observe(.success) { _ in
            // Get the download URL after upload
            imageRef.downloadURL { url, error in
                guard let downloadURL = url else { return }
                let newImage = ImageData2(id: UUID().uuidString, url: downloadURL.absoluteString, area: area, date: self.selectedDate)

                // Create ImageData2 and save to Firestore
                let imageData2 = ImageData2(id: UUID().uuidString, url: downloadURL.absoluteString, area: area, date: self.selectedDate)
                let db = Firestore.firestore()
                db.collection("users").document(userId).collection("images").document(imageData2.id).setData(imageData2.dictionary) { error in
                    if let error = error {
                        print("Error saving image data: \(error.localizedDescription)")
                    } else {
                        print("Image data saved successfully!")
                        // Reset UI state or perform any completion actions
                    }
                }
                images.append(newImage)
            }
            self.uploadProgress = 100 // Set uploadProgress to 100 after successful upload

            // Dismiss the sheet
            self.onCompletion()
            self.dismiss()
        }
      AnalyticsInfoLogger.shared.logEvent("Image_Uploaded", properties: ["area": area.rawValue])

    }

    var body: some View {
        VStack {
            VStack {
                PhotosPicker(selection: $selectedItem, matching: .images) {
                    HStack {
                        Image(systemName: "plus")
                            .font(.caption)
                            .foregroundColor(Color("Background"))
                            .padding()
                            .padding(.horizontal, 15)
                            .background(Color("Background 4"))
                            .cornerRadius(10)
                    }
                    .padding()
                }
                .onChange(of: selectedItem) { newItem in
                    if let newItem {
                        showDatePickerSheet = true
                      AnalyticsInfoLogger.shared.logEvent("Image_Selected_For_Upload")

                    }
                }

                if uploadProgress > 0 && uploadProgress < 100 {
                                  ProgressView(value: uploadProgress)
                                      .id(selectedArea)
                              }
            }
        }
      
        .sheet(isPresented: $showDatePickerSheet) {
            ZStack {
                Color("Background").ignoresSafeArea()
                DatePickerView(selectedDate: $selectedDate, onUpload: {
                    Task {
                        do {
                            try await uploadImage(item: selectedItem!, area: selectedArea)
                        } catch {
                            print("Error uploading image: \(error.localizedDescription)")
                        }
                    }
                    showDatePickerSheet = false
                }, onCancel: {
                    showDatePickerSheet = false
                })
            }
        }
        
    }

    func loadTransferable(from item: PhotosPickerItem) async throws {
        if let data = try await item.loadTransferable(type: Data.self) {
            selectedImage = UIImage(data: data)
        }
    }
}

struct DatePickerView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger
    @Binding var selectedDate: Date
    var onUpload: () -> Void
    var onCancel: () -> Void

    var body: some View {
        VStack(alignment: .center, spacing: 16) {
            VStack {
                Text("Select photo taken date")
                    .font(.custom("KdamThmorPro-Regular", size: 20))
                    .fontWeight(.bold)
                Text("No need exact date. Just select a day in that week")
                    .font(.footnote)
                    .foregroundStyle(Color(UIColor.systemGray2))
            }
            .ignoresSafeArea()
            .padding()
            .background(Color(UIColor(hexString: "#DDE6CC")))

            HStack {
                Spacer()
                Button(action: {
                    onCancel()
                }, label: {
                    Text("Cancel")
                })
            }.padding()

            DatePicker("Select Date", selection: $selectedDate, displayedComponents: .date)
                .datePickerStyle(GraphicalDatePickerStyle())
                .padding()
                .onSubmit {
                    // Update the selectedDate with the new value
                    selectedDate = selectedDate
                }

            Button(action: {
              AnalyticsInfoLogger.shared.logEvent("Image_Upload_Confirmed", properties: ["selected_date": selectedDate])

                onUpload()
            }, label: {
                Text("Add Image")
                .foregroundColor(Color("Background"))
                    .padding()
                    .background(Color("Background 4"))
                    .cornerRadius(8)
            })
            .padding()
        }
        .onAppear {
                   AnalyticsInfoLogger.shared.logEvent("Date_Picker_Opened")
               }
        .padding()
        .cornerRadius(8)
        .background(Color("Background"))
    }
    

}
struct ImageGalleryView: View {
    let selectedArea: Area2
    @ObservedObject var areaManager: AreaManager
    @Binding var images: [ImageData2]
    @Binding var currentIndex: Double
    var contentMode: SwiftUI.ContentMode = .fill

    @State private var imageWidth: CGFloat = 300

    var body: some View {
        VStack {
            ScrollViewReader { proxy in
                ScrollView(.horizontal, showsIndicators: false) {
                    LazyHGrid(rows: [GridItem(.fixed(imageWidth))]) {
                        if images.isEmpty {
                            Text("No images found")
                        } else {
                            ForEach(Array(images.filter { $0.area == selectedArea }.enumerated()), id: \.offset) { index, image in
                                KFImage(URL(string: image.url))
                                    .placeholder {
                                        Image(systemName: "photo")
                                            .resizable()
                                            .scaledToFit()
                                    }
                                    .onFailure { error in
                                        Image(systemName: "exclamationmark.triangle")
                                            .resizable()
                                            .scaledToFit()
                                    }
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: imageWidth, height: 200)
                                    .aspectRatio(contentMode: contentMode)
                                    .id(index) // Use index as the ID
                            }                                    .cornerRadius(10)

                        }

                    }


                }
                .onChange(of: currentIndex) { newIndex in
                    withAnimation {
                        scrollToImage(proxy: proxy, index: Int(newIndex))
                    }
                }

            }

            HStack {
                Slider(value: $currentIndex, in: 0...Double(max(0, images.count - 1))) { _ in
                }
              

                Text("Image \(Int(currentIndex) + 1) of \(images.count)")
            }
            
        }
    }

    // Function to scroll to a specific image index
    private func scrollToImage(proxy: ScrollViewProxy?, index: Int) {
        if let proxy = proxy {
            proxy.scrollTo(index, anchor: .center)
        }
    }
}

struct DirectImageSwitcherView: View {
    let selectedArea: Area2
    @ObservedObject var areaManager: AreaManager
    @Binding var images: [ImageData2]
    var contentMode: SwiftUI.ContentMode = .fill

    @Binding var currentIndex: Double
    @State private var imageWidth: CGFloat = 300
    @State private var showDeleteConfirmation = false

    private var filteredImages: [ImageData2] {
        images.filter { $0.area == selectedArea }
    }

    private var currentFilteredIndex: Int {
        min(Int(currentIndex), filteredImages.count - 1)
    }

    var body: some View {
        VStack {
            if filteredImages.isEmpty {
                Text("Add your images for \(selectedArea.rawValue) area of your head")
            } else {
                VStack {
                    ZStack(alignment: .topTrailing) {
                        KFImage(URL(string: filteredImages[currentFilteredIndex].url ?? ""))
                            .placeholder {
                                Image(systemName: "photo")
                                    .resizable()
                                    .scaledToFit()
                            }
                            .onFailure { error in
                                Image(systemName: "exclamationmark.triangle")
                                    .resizable()
                                    .scaledToFit()
                            }
                            .resizable()
                            .scaledToFit()
                            .frame(width: imageWidth, height: 200)
                            .aspectRatio(contentMode: contentMode)
                            .transition(.opacity)
                            .cornerRadius(10)

                        Button(action: {
                            showDeleteConfirmation = true
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.red)
                                .background(Color.white)
                                .clipShape(Circle())
                        }
                        .padding(8)
                    }

                    Text(formatDate(date: filteredImages[currentFilteredIndex].date))
                        .font(.caption)
                        .foregroundColor(.gray)
                        .padding(.top, 4)
                }
            }

            HStack {
                Slider(value: $currentIndex, in: 0...Double(max(0, filteredImages.count - 1))) { _ in
                    // No action needed here
                }

                Text("\(currentFilteredIndex + 1) of \(filteredImages.count)")
            }
            .padding()
            .padding(.horizontal, 20)
        }
        .padding(.top, 50)
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
        .alert(isPresented: $showDeleteConfirmation) {
            Alert(
                title: Text("Delete Image"),
                message: Text("Are you sure you want to delete this image?"),
                primaryButton: .destructive(Text("Delete")) {
                    deleteCurrentImage()
                },
                secondaryButton: .cancel()
            )
        }
    }

    private func formatDate(date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter.string(from: date)
    }

    private func deleteCurrentImage() {
        guard !filteredImages.isEmpty else { return }
        let imageToDelete = filteredImages[currentFilteredIndex]

        // Delete from Firestore
        deleteImageFromFirestore(imageToDelete)

        // Remove from local array
        if let index = images.firstIndex(where: { $0.id == imageToDelete.id }) {
            images.remove(at: index)
        }

        // Adjust currentIndex if necessary
        if filteredImages.isEmpty {
            currentIndex = 0
        } else if currentIndex >= Double(filteredImages.count) {
            currentIndex = Double(filteredImages.count - 1)
        }
    }

    private func deleteImageFromFirestore(_ image: ImageData2) {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        let db = Firestore.firestore()
        db.collection("users").document(userId).collection("images").document(image.id).delete { error in
            if let error = error {
                print("Error deleting image: \(error.localizedDescription)")
            } else {
                print("Image successfully deleted from Firestore")
                // You may want to delete the image from Firebase Storage as well
                deleteImageFromStorage(url: image.url)
            }
        }
    }

    private func deleteImageFromStorage(url: String) {
        guard let imageUrl = URL(string: url) else { return }
        let storageRef = Storage.storage().reference(forURL: imageUrl.absoluteString)
        storageRef.delete { error in
            if let error = error {
                print("Error deleting image from storage: \(error.localizedDescription)")
            } else {
                print("Image successfully deleted from storage")
            }
        }
    }
}


struct DatePickerView_Previews: PreviewProvider {
    static var previews: some View {
        DatePickerView(selectedDate: .constant(Date()), onUpload: {
            // Upload logic
        }, onCancel: {
            // Cancel logic
        })
    }
}
