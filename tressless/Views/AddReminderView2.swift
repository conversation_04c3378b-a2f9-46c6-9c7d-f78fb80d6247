//
//  AddReminderView2.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI

struct AddReminderView2: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger

    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel = AddReminderViewModel()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Time Picker
                VStack(alignment: .leading) {
                    Text("Time")
                        .font(.title2)
                        .bold()
                    
                    DatePicker("", selection: $viewModel.time, displayedComponents: .hourAndMinute)
                        .labelsHidden()
                    
                    Picker("Repeat", selection: $viewModel.reminderRepeat) {
                        ForEach(ReminderRepeat.allCases, id: \.self) { reminderRepeat in
                            Text(reminderRepeat.rawValue.uppercased())
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    
                    // Dosage
                    VStack(alignment: .leading) {
                        Text("Dosage")
                            .font(.title2)
                            .bold()
                        
                        TextField("875 mg", value: $viewModel.dosage, formatter: NumberFormatter())
                            .keyboardType(.numberPad)
                            .padding()
                            .background(Color(UIColor.secondarySystemBackground))
                            .cornerRadius(10)
                    }
                    
                    // Reminder Settings
                    VStack(alignment: .leading) {
                        Text("Reminder Settings")
                            .font(.title2)
                            .bold()
                        
                        HStack {
                            Picker("Amount", selection: $viewModel.amount) {
                                ForEach(1..<11) { amount in
                                    Text("\(amount) PILL")
                                }
                            }
                            
                            Picker("Peculiarities", selection: $viewModel.peculiarities) {
                                Text("BEFORE MEAL")
                                Text("AFTER MEAL")
                            }
                            
                            Picker("Frequency", selection: $viewModel.frequency) {
                                ForEach(1..<8) { frequency in
                                    Text("\(frequency) TIME A DAY")
                                }
                            }
                            
                            Picker("Duration", selection: $viewModel.duration) {
                                ForEach(1..<5) { duration in
                                    Text("\(duration) WEEK")
                                }
                            }
                        }
                    }
                    
                    Spacer()
                    
                    Button(action: {
                      viewModel.logger = logger
                                        viewModel.addReminder()
                                        presentationMode.wrappedValue.dismiss()
                                        AnalyticsInfoLogger.shared.logEvent("Reminder_Added_Button_Tapped")
                    }) {
                        Text("ADD MEDICINE")
                            .font(.title2)
                            .bold()
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                }
                .onChange(of: viewModel.reminderRepeat) { newValue in
                            AnalyticsInfoLogger.shared.logEvent("Reminder_Repeat_Changed", properties: ["value": newValue.rawValue])
                        }
                        .onChange(of: viewModel.dosage) { newValue in
                            AnalyticsInfoLogger.shared.logEvent("Dosage_Changed", properties: ["value": newValue])
                        }
                        .onChange(of: viewModel.amount) { newValue in
                            AnalyticsInfoLogger.shared.logEvent("Amount_Changed", properties: ["value": newValue])
                        }
                        .onChange(of: viewModel.peculiarities) { newValue in
                            AnalyticsInfoLogger.shared.logEvent("Peculiarities_Changed", properties: ["value": newValue])
                        }
                        .onChange(of: viewModel.frequency) { newValue in
                            AnalyticsInfoLogger.shared.logEvent("Frequency_Changed", properties: ["value": newValue])
                        }
                        .onChange(of: viewModel.duration) { newValue in
                            AnalyticsInfoLogger.shared.logEvent("Duration_Changed", properties: ["value": newValue])
                        }
                        .onAppear {
                            AnalyticsInfoLogger.shared.logEvent("Add_Reminder_View_Opened")
                        }
                .padding()
                .navigationBarTitle("Add Reminder", displayMode: .inline)
                .navigationBarItems(trailing: Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Text("Cancel")
                })
            }
            .onAppear {
                          AnalyticsInfoLogger.shared.logEvent("Add_Reminder2_View_Opened")
                      }
        }
    }
    
    class AddReminderViewModel: ObservableObject {
        @Published var time = Date()
        @Published var reminderRepeat: ReminderRepeat = .everyday
        @Published var dosage = 875
        @Published var amount = 1
        @Published var peculiarities = "AFTER MEAL"
        @Published var frequency = 1
        @Published var duration = 1
      var logger: AnalyticsInfoLogger?

        func addReminder() {
            // Add the reminder to the app's data model
            print("Added reminder: \(time), \(reminderRepeat), \(dosage) mg, \(amount) pill, \(peculiarities), \(frequency) times a day, \(duration) weeks")
          logger?.logEvent("Reminder_Added", properties: [
                  "time": time.description,
                  "repeat": reminderRepeat.rawValue,
                  "dosage": dosage,
                  "amount": amount,
                  "peculiarities": peculiarities,
                  "frequency": frequency,
                  "duration": duration
              ])
        }
    }
    
    struct ReminderRepeat: Identifiable, Hashable {
        let id = UUID()
        let rawValue: String
        
        static let everyday = ReminderRepeat(rawValue: "Everyday")
        static let inAnHour = ReminderRepeat(rawValue: "In an hour")
        static let inTwoHours = ReminderRepeat(rawValue: "In two hours")
        static let noReminder = ReminderRepeat(rawValue: "No reminder")
        
        static var allCases: [ReminderRepeat] {
            [.everyday, .inAnHour, .inTwoHours, .noReminder]
        }
    }
    
    struct AddReminderView_Previews: PreviewProvider {
        static var previews: some View {
            AddReminderView2()
        }
    }
}

