import SwiftUI

struct ToolsView: View {
  @Binding var selectedTab: Tab
    @State private var showClassifyView = false
    @State private var showPathView = false
    @State private var analysisCompleted = false
    @StateObject private var medicationManager = MedicationManager()
    @State private var medications: [MedicationDocument] = []
    
    // Add this environment object to handle deep links
    @EnvironmentObject var deepLinkHandler: DeepLinkHandler
    
    var body: some View {
        VStack(spacing: 10) {
            HStack(spacing: 10) {
                ToolCard(
                    title: "Hair loss analyze",
                    subtitle: "Check your hair density with AI",
                    iconName: "headlight.daytime",
                    color: Color(hex: "d6d0fd"),
                    height: 250,
                    fontColor: Color.black,
                    action: { showClassifyView = true }
                )
                
                VStack(spacing: 10) {
                    ToolCard(
                        title: "Progress Tracker",
                        subtitle: "",
                        iconName: "headlight.daytime",
                        color: Color(hex: "feecba"),
                        height: 150,
                        fontColor: Color.black,
                        action: { selectedTab = .bell }
                    )
                    
                    ToolCard(
                        title: "Regain hair",
                        subtitle: "",
                        iconName: "headlight.daytime",
                        color: Color(hex: "25252a"),
                        height: 150,
                        fontColor: Color.white,
                        action: { selectedTab = .search }
                    )
                }
            }
            
            ToolCard(
                title: "Medication Paths",
                subtitle: "",
                iconName: "headlight.daytime",
                color: Color(hex: "DDE6CC"),
                height: 100,
                fontColor: Color.black,
                action: { showPathView = true }
            )
        }
        .sheet(isPresented: $showClassifyView) {
            ClassifyView(onCompletion: { success in
                analysisCompleted = success
                if success {
                    print("Analysis completed successfully.")
                } else {
                    print("Analysis failed or was cancelled.")
                    showClassifyView = false
                }
            })
        }
        .sheet(isPresented: $showPathView) {
            PathView(
                medications: $medications,
                selectedTab: $selectedTab,
                medicationManager: medicationManager,
                onPathEnrolled: { success in
                    if success {
                        print("Path enrolled successfully")
                        showPathView = false
                    } else {
                        print("Path enrollment failed")
                    }
                }
            )
        }
        .onReceive(deepLinkHandler.$action) { action in
            if action == .openClassifyView {
                showClassifyView = true
                deepLinkHandler.action = nil
            }
        }
    }
}

struct ToolCard: View {
    let title: String
    let subtitle: String
    let iconName: String
    let color: Color
    let height: CGFloat
    let fontColor: Color
    var action: (() -> Void)? = nil
    
    var body: some View {
        Button(action: { action?() }) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: iconName)
                        .padding(10)
                        .background(Color.white.opacity(0.3))
                        .clipShape(Circle())
                    Spacer()
                    Image(systemName: "arrow.up.right")
                }
                Spacer()
                Text(title)
                .font(subtitle.isEmpty ? .caption : .title)
                if !subtitle.isEmpty {
                    Text(subtitle)
                    .font(.caption)
                }
            }
            .padding(20)
            .frame(maxWidth: .infinity, maxHeight: height)
            .foregroundColor(fontColor)
            .background(color)
            .clipShape(RoundedRectangle(cornerRadius: 20, style: .continuous))
        }
        .buttonStyle(PlainButtonStyle())
    }
}


#Preview {
    ToolsView(selectedTab: .constant(.chat))
}
