//
//  ProfileView.swift
//  tressless
//
//  Created by <PERSON> on 29.08.2024.
//

import SwiftUI
import StoreKit
import Firebase
import FirebaseFirestore
import RevenueCat
import FirebaseAuth

struct ProfileView: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger
    @Environment(\.auth) private var authManager
    @EnvironmentObject var root: RootState
    @State private var showPaywall = false
    @State private var subscriptionType: String = "free"
    @State private var isLoading = true
    @State private var showDeleteAccountAlert = false
    @State private var analysesLeft: Int = 0
    
    var body: some View {
        NavigationView {
            List {
                if isLoading {
                    ProgressView()
                } else {
                    Section(header: Text("Subscription")) {
                        if subscriptionType != "free" {
                            Text("Pro Version")
                                .foregroundColor(.green)
                        } else {
                            Text("Free Version")
                            Button("Upgrade to Pro") {
                                showPaywall = true
                                AnalyticsInfoLogger.shared.logEvent("Upgrade_To_Pro_Tapped")
                            }
                        }
                        
                        // Add this new Text view to display the analyses left
                        Text("Analyses left: \(analysesLeft)")
                    }
                    
                    Section {
                        Link("Contact Support", destination: URL(string: "https://follicleai.com/contact")!)

                    }
                    
                    Section {
                        Link("Privacy Policy", destination: URL(string: "https://follicleai.com/privacy-policy")!)
                        Link("Terms of Service", destination: URL(string: "https://follicleai.com/terms-and-conditions")!)
                    }
                    
                    Section {
                        Button("Sign Out") {
                            onSignOutPressed()
                        }
                        .foregroundColor(.red)

                        Button("Delete Account") {
                            showDeleteAccountAlert = true
                        }
                        .foregroundColor(.red)
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("Profile")
        }
        .sheet(isPresented: $showPaywall) {
            PayView(
                onDismiss: {
                    showPaywall = false
                    fetchSubscriptionInfo()
                },
                onPurchaseComplete: {
                    showPaywall = false
                    fetchSubscriptionInfo()
                    AnalyticsInfoLogger.shared.logEvent("Purchase_Completed_From_Profile")
                },
                placementId: "onboarding",
                currentStep: .constant(0)
            )
        }
        .onChange(of: showPaywall) { newValue in
            if !newValue {
                // This will be called when the sheet is dismissed
                fetchSubscriptionInfo()
            }
        }
        .onAppear {
            AnalyticsInfoLogger.shared.logEvent("Profile_View_Opened")
            fetchSubscriptionInfo()
            fetchAnalysesLeft()
        }
        .alert("Delete Account", isPresented: $showDeleteAccountAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteAccount()
            }
        } message: {
            Text("Are you sure you want to delete your account? This action cannot be undone.")
        }
    }
    
    private func fetchSubscriptionInfo() {
        guard let userId = Auth.auth().currentUser?.uid else {
            isLoading = false
            return
        }

        isLoading = true

        // Sync subscription status with Firebase (like main2)
        Task {
            do {
                let customerInfo = try await Purchases.shared.customerInfo()
                let isPremium = customerInfo.entitlements["premium"]?.isActive ?? false
                print("RevenueCat subscription status - Premium: \(isPremium)")

                // Update Firebase with current RevenueCat status
                await syncSubscriptionStatus(userId: userId, isPremium: isPremium)

            } catch {
                print("Failed to get RevenueCat customer info: \(error)")
            }
        }

        let db = Firestore.firestore()
        let subscriptionRef = db.collection("users").document(userId).collection("subscriptions").document("current_subscription")

        subscriptionRef.getDocument { (document, error) in
            DispatchQueue.main.async {
                self.isLoading = false

                if let document = document, document.exists {
                    if let subscriptionType = document.data()?["subscriptionType"] as? String {
                        self.subscriptionType = subscriptionType

                        AnalyticsInfoLogger.shared.logEvent("Subscription_Fetched", properties: ["type": subscriptionType])
                    }
                } else {
                    print("Document does not exist or error: \(error?.localizedDescription ?? "Unknown error")")
                }
            }
        }
    }

    // Sync subscription status with Firebase (matches main2 approach)
    @MainActor
    private func syncSubscriptionStatus(userId: String, isPremium: Bool) async {
        let db = Firestore.firestore()
        let subscriptionRef = db.collection("users").document(userId)
            .collection("subscriptions").document("current_subscription")

        let subscriptionData: [String: Any] = [
            "subscriptionType": isPremium ? "premium" : "free",
            "analyzesLeft": isPremium ? 1000 : 0,
            "lastChecked": Timestamp(date: Date())
        ]

        do {
            try await subscriptionRef.setData(subscriptionData, merge: true)
            print("Subscription status synced from ProfileView: \(isPremium ? "premium" : "free")")
        } catch {
            print("Failed to sync subscription status: \(error.localizedDescription)")
        }
    }

    private func fetchAnalysesLeft() {
        guard let userId = Auth.auth().currentUser?.uid else {
            return
        }
        
        let db = Firestore.firestore()
        let subscriptionRef = db.collection("users").document(userId).collection("subscriptions").document("current_subscription")
        
        subscriptionRef.getDocument { (document, error) in
            if let document = document, document.exists {
                if let analysesLeft = document.data()?["analyzesLeft"] as? Int {
                    DispatchQueue.main.async {
                        self.analysesLeft = analysesLeft
                    }
                }
            } else {
                print("Document does not exist or error: \(error?.localizedDescription ?? "Unknown error")")
            }
        }
    }
    
    @MainActor
    private func onSignOutPressed() {
        do {
            try authManager.signOut()

            clearAppStorage()
            clearCache()

            // 🎯 SMART SIGN OUT: Let RootView handle routing based on subscription status
            // Don't directly set showTabbar - let smart detection decide the flow
            root.updateViewState(showTabbar: false)

            AnalyticsInfoLogger.shared.logEvent("User_Signed_Out")
        } catch {
            print("Error signing out: \(error)")
        }
    }
    
    private func deleteAccount() {
        guard let user = Auth.auth().currentUser else { return }

        // Delete user data from Firestore
        let db = Firestore.firestore()
        db.collection("users").document(user.uid).delete { error in
            if let error = error {
                print("Error deleting user data: \(error.localizedDescription)")
            }
        }

        // Delete the user account
        user.delete { error in
            if let error = error {
                print("Error deleting user account: \(error.localizedDescription)")
            } else {
                DispatchQueue.main.async {
                    // Clear local data
                    clearAppStorage()
                    clearCache()

                    // Update view state
                    root.updateViewState(showTabbar: false)

                    // Log event
                    AnalyticsInfoLogger.shared.logEvent("User_Account_Deleted")

                    // Sign out (this will redirect to the login screen)
                    try? authManager.signOut()
                }
            }
        }
    }
}

struct ProfileView_Previews: PreviewProvider {
    static var previews: some View {
        ProfileView()
    }
}

#Preview {
    ProfileView()
}

private func clearAppStorage() {
    if let appDomain = Bundle.main.bundleIdentifier {
        UserDefaults.standard.removePersistentDomain(forName: appDomain)
    }
    UserDefaults.standard.synchronize()
}

private func clearCache() {
    URLCache.shared.removeAllCachedResponses()
    
    let tempDirectory = FileManager.default.temporaryDirectory
    do {
        let tempFiles = try FileManager.default.contentsOfDirectory(atPath: tempDirectory.path)
        for file in tempFiles {
            let filePath = tempDirectory.appendingPathComponent(file).path
            try FileManager.default.removeItem(atPath: filePath)
        }
    } catch {
        print("Error clearing temporary files: \(error.localizedDescription)")
    }
}
