//
//  PathView.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI
import FirebaseAuth
import FirebaseFirestore

struct PathView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger

    @Environment(\.dismiss) var dismiss
    @Binding var medications: [MedicationDocument]
  @Binding var selectedTab: Tab

    @ObservedObject var medicationManager: MedicationManager
    var onPathEnrolled: (Bool) -> Void
    
    let paths: [HairGrowthPath] = [
        HairGrowthPath(
            name: "Topical Treatments and Scalp Stimulation",
            medications: [
                MedicationDocument(id: UUID().uuidString, name: "Minoxidil 5% solution", time: Date(), repeatInterval: .daily, dosage: "Apply daily as directed.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Dermaroller 0.5mm-1.0mm", time: Date(), repeatInterval: .weekly, dosage: "Use every week, focusing on areas with thinning hair.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Ketoconazole shampoo (2%)", time: Date(), repeatInterval: .weekly, dosage: "Use every week to combat any fungal issues and inflammation.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Scalp massage", time: Date(), repeatInterval: .daily, dosage: "Perform daily to improve blood flow and nutrient delivery.", dates: [])
            ]
        ),
        HairGrowthPath(
            name: "Combine Topical Treatments with Oral Supplements",
            medications: [
                MedicationDocument(id: UUID().uuidString, name: "Minoxidil 5% solution", time: Date(), repeatInterval: .daily, dosage: "Apply daily as directed.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Saw Palmetto supplement", time: Date(), repeatInterval: .daily, dosage: "Take as directed by the manufacturer to potentially block DHT, a hormone linked to hair loss. once a day.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Multivitamin with biotin", time: Date(), repeatInterval: .daily, dosage: "Take daily to ensure adequate intake of essential vitamins and minerals.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Scalp massage", time: Date(), repeatInterval: .daily, dosage: "Perform daily.", dates: [])
            ]
        ),
        HairGrowthPath(
            name: "Intensive Growth Stimulation with PRP",
            medications: [
                MedicationDocument(id: UUID().uuidString, name: "PRP (Platelet-Rich Plasma) injections", time: Date(), repeatInterval: .monthly, dosage: "Administered by a medical professional every month to promote hair growth through growth factors.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Minoxidil 5% solution", time: Date(), repeatInterval: .daily, dosage: "Apply once daily.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Ketoconazole shampoo (2%)", time: Date(), repeatInterval: .weekly, dosage: "Use every week.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Scalp massage", time: Date(), repeatInterval: .daily, dosage: "Perform daily.", dates: [])
            ]
        ),
        HairGrowthPath(
            name: "Natural Approach with Lifestyle Modifications",
            medications: [
                MedicationDocument(id: UUID().uuidString, name: "Scalp massage", time: Date(), repeatInterval: .daily, dosage: "Perform daily.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Diet rich in hair-healthy foods", time: Date(), repeatInterval: .daily, dosage: "Focus on consuming foods rich in protein, iron, biotin, and other essential nutrients daily.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Stress management techniques", time: Date(), repeatInterval: .daily, dosage: "Practice yoga, meditation, or other relaxation techniques to manage stress levels, as stress can contribute to hair loss.", dates: []),
                MedicationDocument(id: UUID().uuidString, name: "Essential oil scalp treatments", time: Date(), repeatInterval: .daily, dosage: "Explore using diluted rosemary, peppermint, or lavender oil on the scalp to potentially stimulate hair growth everyday.", dates: [])
            ]
        )
    ]
    
    
  var body: some View {
    NavigationView{
      
      VStack {
        HStack {
          Text("Select Your Path")
            .font(.custom("KdamThmorPro-Regular", size: 20))
            .fontWeight(.bold)
            .padding(.top)
          
        }

        List {
          ForEach(paths) { path in
            NavigationLink(destination: PathDetailView(selectedTab: $selectedTab, path: path, medications: $medications, medicationManager: medicationManager, onPathEnrolled: onPathEnrolled)) {
              HStack {
                
                
                VStack(alignment: .leading, spacing: 4) {
                  Text(path.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(Color("Background 2"))

                  HStack {
                    ForEach(path.medications.prefix(4), id: \.name) { medication in
                      Text(medication.name.components(separatedBy: " ").first ?? "")
                        .foregroundColor(.white)
                        .padding(6)
                        .background(Color("Background 2"))
                        .font(.footnote)
                        .cornerRadius(10)
                    }
                  }
                  /* Text("1450+ User Enrolled")
                    .font(.footnote)
                    .foregroundColor(.gray)
                   */
                }
                
                Spacer()
              }
              .padding()
              .background(Color(UIColor(hexString: "#FCEBC4")))
              .cornerRadius(10)

            }
            

          }
          .listRowBackground(Color("Background"))

        }
        .listStyle(.plain)
        .listRowBackground(Color("Background"))
        
        Text("You can change or customize later")
          .font(.footnote)
          .foregroundColor(.gray)
          .padding(.top, 8)
      }
      .background(Color("Background"))
    }
    .onAppear {
               AnalyticsInfoLogger.shared.logEvent("Path_View_Opened")
           }
   

    }
  
}

struct PathDetailView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger

    @Environment(\.dismiss) var dismiss
    @Environment(\.presentationMode) var presentationMode
  @Binding var selectedTab: Tab

    let path: HairGrowthPath
    @Binding var medications: [MedicationDocument]
    @ObservedObject var medicationManager: MedicationManager
    var onPathEnrolled: (Bool) -> Void
    
    @State private var showMedicationView = false
    @State private var showAlert = false
    @State private var currentPath: HairGrowthPath?
    
    var body: some View {
       
            VStack {
                List(path.medications) { medication in
                    Section {
                        MedicationRowView(medication: medication)
                    }
                    .listRowBackground(Color("Background"))
                }
                .listStyle(.plain)
                
              Text("You can personalize it later by adding or removing your medications.")
                .font(.footnote)
                .fontWeight(.ultraLight)
              
                Button(action: {
                    enrollPath(path)
                  

                }) {
                    HStack {
                        Image(systemName: "plus")
                        Text("Enroll Path")
                    }
                    .padding()
                    .background(Color.black)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .padding(.bottom, 66)
              
                NavigationLink(destination: SearchView(), isActive: $showMedicationView) {
                    EmptyView()
                }
            }
            .onAppear {
                      AnalyticsInfoLogger.shared.logEvent("Path_Detail_Viewed", properties: ["path_name": path.name])
                  }
            .background(Color("Background"))
            .scrollContentBackground(.hidden)
            .navigationBarTitle("Path Details", displayMode: .inline)
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text("Already Enrolled"),
                    message: Text("You are already enrolled in another path. Clicking OK will delete your existing medications and paths."),
                    primaryButton: .destructive(Text("OK")) {
                        deleteExistingMedicationsAndEnrollPath()
                    },
                    secondaryButton: .cancel()
                )
            }
          Spacer()
        
    }
    
    func enrollPath(_ path: HairGrowthPath) {
      AnalyticsInfoLogger.shared.logEvent("Path_Enrollment_Started", properties: ["path_name": path.name])

        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        let pathsRef = db.collection("users").document(userId).collection("paths")
        
        pathsRef.getDocuments { snapshot, error in
            if let error = error {
                print("Error fetching paths: \(error)")
                return
            }
            
            if let existingPath = snapshot?.documents.first {
                // Show alert to confirm deletion
                self.showAlert = true
                self.currentPath = path
            } else {
                // Enroll directly if no existing path
                self.enrollPathWithoutCheck(path)
            }
        }
    }
    
    func enrollPathWithoutCheck(_ path: HairGrowthPath) {
      AnalyticsInfoLogger.shared.logEvent("Path_Enrolled", properties: ["path_name": path.name])

        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        for medication in path.medications {
            let medicationRef = db.collection("users").document(userId).collection("medications").document()
            let id = medicationRef.documentID
            
            let timestampTime = Timestamp(date: medication.time)
            var dates: [Date] = []
            
            switch medication.repeatInterval {
            case .daily:
                let calendar = Calendar.current
                let startDate = calendar.startOfDay(for: medication.time)
                var currentDate = startDate
                while currentDate <= calendar.date(byAdding: .year, value: 1, to: startDate)! {
                    dates.append(currentDate)
                    currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
                }
            case .weekly:
                let calendar = Calendar.current
                let startDate = calendar.startOfDay(for: medication.time)
                var currentDate = startDate
                while currentDate <= calendar.date(byAdding: .year, value: 1, to: startDate)! {
                    dates.append(currentDate)
                    currentDate = calendar.date(byAdding: .weekOfYear, value: 1, to: currentDate)!
                }
            case .monthly:
                let calendar = Calendar.current
                let startDate = calendar.startOfDay(for: medication.time)
                var currentDate = startDate
                while currentDate <= calendar.date(byAdding: .year, value: 1, to: startDate)! {
                    dates.append(currentDate)
                    currentDate = calendar.date(byAdding: .month, value: 1, to: currentDate)!
                }
            case .noReminder:
                break
            }
            
            let timestampDates = dates.map { Timestamp(date: $0) }
            
            do {
                try medicationRef.setData([
                    "id": id,
                    "name": medication.name,
                    "time": timestampTime,
                    "repeatInterval": medication.repeatInterval.rawValue,
                    "dosage": medication.dosage,
                    "dates": timestampDates
                ])
                
                let newMedication = MedicationDocument(id: id, name: medication.name, time: medication.time, repeatInterval: medication.repeatInterval, dosage: medication.dosage, dates: dates)
                medications.append(newMedication)
            } catch {
                print("Error adding medication: \(error)")
            }
        }
        
        // Store the enrolled path information
        let pathRef = db.collection("users").document(userId).collection("paths").document()
        let enrolledDate = Date()
        
        do {
            try pathRef.setData([
                "name": path.name,
                "enrolledDate": Timestamp(date: enrolledDate)
            ])
        } catch {
            print("Error storing enrolled path: \(error)")
        }
        
        // Navigate to MedicationView
        showMedicationView = true
      selectedTab = .search

        // Call the completion handler
        onPathEnrolled(true)
    }
    
    func deleteExistingMedicationsAndEnrollPath() {
      AnalyticsInfoLogger.shared.logEvent("Existing_Medications_Deleted")

        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        let medicationsRef = db.collection("users").document(userId).collection("medications")
        let pathsRef = db.collection("users").document(userId).collection("paths")
        let completionsRef = db.collection("users").document(userId).collection("medication_completions")
        
        let batch = db.batch()
        
        // Delete all medications
        medicationsRef.getDocuments { snapshot, error in
            if let error = error {
                print("Error fetching medications: \(error)")
                return
            }
            
            snapshot?.documents.forEach { document in
                batch.deleteDocument(document.reference)
            }
            
            // Delete all paths
            pathsRef.getDocuments { snapshot, error in
                if let error = error {
                    print("Error fetching paths: \(error)")
                    return
                }
                
                snapshot?.documents.forEach { document in
                    batch.deleteDocument(document.reference)
                }
                
                // Delete all medication completions
                completionsRef.getDocuments { snapshot, error in
                    if let error = error {
                        print("Error fetching medication completions: \(error)")
                        return
                    }
                    
                    snapshot?.documents.forEach { document in
                        batch.deleteDocument(document.reference)
                    }
                    
                    // Commit the batch
                    batch.commit { error in
                        if let error = error {
                            print("Error deleting documents: \(error)")
                            return
                        }
                        
                        if let path = self.currentPath {
                            self.enrollPathWithoutCheck(path)
                            selectedTab = .search
                        }
                    }
                }
            }
        }
    }
}




struct HairGrowthPath: Identifiable {
    let id = UUID()
    let name: String
    let medications: [MedicationDocument]
    var enrolledDate: Date? // Add this new property

}

struct MedicationListView: View {
    let medications: [MedicationDocument]
    
    var body: some View {
        List(medications) { medication in
            VStack(alignment: .leading) {
                Text(medication.name)
                    .font(.headline)
                Text(medication.dosage)
                    .font(.subheadline)
                Text("Repeat: \(medication.repeatInterval.rawValue.capitalized)")
                    .font(.subheadline)
            }
        }
        .navigationBarTitle("Path Details")
    }
}





struct MedicationRowView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger

    var medication: MedicationDocument
    
    var body: some View {
        
        HStack {
            Image(systemName: "capsule.fill")
                .foregroundColor(Color("Background 2"))
                .padding(6)
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 8) {
                    Text(medication.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(Color("Background 2"))
                    Spacer()
                    Text(medication.time, style: .time)
                        .font(.subheadline)
                        .foregroundColor(Color("Background 2"))
                }
                .onTapGesture {
                            AnalyticsInfoLogger.shared.logEvent("Medication_Row_Tapped", properties: ["medication_name": medication.name])
                        }
                Text("\(medication.dosage) \(medication.repeatInterval.rawValue.capitalized)")
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            .padding(6)
            
            Spacer()
        }
        .padding()
        .background(Color(UIColor(hexString: "#DDEAF8")))
        .cornerRadius(10)
    }
}

