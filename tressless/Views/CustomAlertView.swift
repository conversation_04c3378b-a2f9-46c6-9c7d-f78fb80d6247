import SwiftUI

struct CustomAlertView: View {
    let message: String
    let footnote: String
    let buttonTitle: String
    let action: () -> Void
    let dismiss: () -> Void
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.3)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    dismiss()
                }
            
            VStack(spacing: 20) {
                Text(message)
                    .font(.subheadline)
                    .multilineTextAlignment(.center)
                Button(action: {
                    action()
                    dismiss()
                }) {
                    Text(buttonTitle)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                Text("\(Image(systemName: "checkmark.shield")) \(footnote)")
                    .font(.caption)
                    .foregroundColor(Color("Background"))
                    .opacity(0.8)
                    .multilineTextAlignment(.center)
            }
            .padding()
            .foregroundColor(Color("Background"))
            .background(Color("Background 4"))
            .cornerRadius(15)
            .padding(30)
            .frame(maxWidth: 300)
        }
    }
}

#Preview {
    CustomAlertView(
        message: "You have no analyses left. Please upgrade your subscription to have unlimited analyses.",
        footnote: "10,000+ customers say the Pro Plan helped with their hair regrowth process. Cancel anytime.",
        buttonTitle: "Upgrade to Pro",
        action: {},
        dismiss: {}
    )
}
