//
//  FirstNoticeChangesnView.swift
//  tressless
//
//  Created by <PERSON> on 13/06/2025.
//

import SwiftUI

// FirstNoticeChangesView
struct FirstNoticeChangesView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger
    @State private var selectedFirstNoticeTime: String?
    
    var body: some View {
        VStack(spacing: 20) {
          Image("age")
              .resizable()
              .aspectRatio(contentMode: .fit)
              .frame(height: 200)
              .padding()
          Spacer()
            Text("When did you first notice changes to your hair?")
                .font(.title2)
                .padding()
                .fixedSize(horizontal: false, vertical: true)
            
          SelectionButton(title: "Within the last 6 months", isSelected: selectedFirstNoticeTime == "Within the last 6 months") {
              selectedFirstNoticeTime = "Within the last 6 months"
          }
          
          SelectionButton(title: "1-2 years ago", isSelected: selectedFirstNoticeTime == "1-2 years ago") {
              selectedFirstNoticeTime = "1-2 years ago"
          }
          
          SelectionButton(title: "Over 2 years ago", isSelected: selectedFirstNoticeTime == "Over 2 years ago") {
              selectedFirstNoticeTime = "Over 2 years ago"
          }
            
          SelectionButton(title: "I'm not sure", isSelected: selectedFirstNoticeTime == "I'm not sure") {
              selectedFirstNoticeTime = "I'm not sure"
          }
            
         
        }
        .padding()
    }
}


#Preview {
    FirstNoticeChangesView()
}
