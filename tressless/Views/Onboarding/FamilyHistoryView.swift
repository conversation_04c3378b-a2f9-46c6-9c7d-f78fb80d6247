//
//  FamilyHistoryView.swift
//  tressless
//
//  Created by <PERSON> on 29/04/2025.
//


import SwiftUI

// FamilyHistoryView
struct FamilyHistoryView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger
    @State private var selectedFamilyHistory: String?
    
    var body: some View {
        VStack(spacing: 20) {
          Image("age")
              .resizable()
              .aspectRatio(contentMode: .fit)
              .frame(height: 200)
              .padding()
          Spacer()
            Text("Do you have a family history of hair loss?")
                .font(.title)
                .padding()
            
          SelectionButton(title: "Alot", isSelected: selectedFamilyHistory == "Alot") {
              selectedFamilyHistory = "Alot"
          }
          
          SelectionButton(title: "Some", isSelected: selectedFamilyHistory == "Some") {
              selectedFamilyHistory = "Some"
          }
          
          SelectionButton(title: "None", isSelected: selectedFamilyHistory == "None") {
              selectedFamilyHistory = "None"
          }
         
        }
        .padding()
    }
}


#Preview {
    FamilyHistoryView()
}
