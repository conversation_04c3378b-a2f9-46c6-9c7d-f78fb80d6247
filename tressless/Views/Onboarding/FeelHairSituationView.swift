//
//  FeelHairSituationView.swift
//  tressless
//
//  Created by <PERSON> on 13/06/2025.
//

import SwiftUI

// FeelHairSituationView
struct FeelHairSituationView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger
    @State private var selectedFeel: String?
    
    var body: some View {
        VStack(spacing: 20) {
          Image("age")
              .resizable()
              .aspectRatio(contentMode: .fit)
              .frame(height: 200)
              .padding()
          Spacer()
            Text("How do you feel about your current hair situation?")
                .font(.title2)
                .padding()
                .fixedSize(horizontal: false, vertical: true)
            
          SelectionButton(title: "It's starting to bother me", isSelected: selectedFeel == "It's starting to bother me") {
              selectedFeel = "It's starting to bother me"
          }
          
          SelectionButton(title: "I'm actively looking for solutions", isSelected: selectedFeel == "I'm actively looking for solutions") {
              selectedFeel = "I'm actively looking for solutions"
          }
          
          SelectionButton(title: "I've accepted it, but I'd like options", isSelected: selectedFeel == "I've accepted it, but I'd like options") {
              selectedFeel = "I've accepted it, but I'd like options"
          }
            
          SelectionButton(title: "I'm just curious for now", isSelected: selectedFeel == "I'm just curious for now") {
              selectedFeel = "I'm just curious for now"
          }
            
         
        }
        .padding()
    }
}


#Preview {
    FeelHairSituationView()
}

