//
//  ScalpConditionView.swift
//  tressless
//
//  Created by <PERSON> on 29/04/2025.
//

import SwiftUI

// ScalpConditionView
struct ScalpConditionView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger
    @State private var selectedScalpCondition: String?
    
    var body: some View {
        VStack(spacing: 20) {
          Image("age")
              .resizable()
              .aspectRatio(contentMode: .fit)
              .frame(height: 200)
              .padding()
          Spacer()
            Text("What is your scalp's condition?")
                .font(.title2)
                .padding()
            
          SelectionButton(title: "Dry", isSelected: selectedScalpCondition == "Dry") {
              selectedScalpCondition = "Dry"
          }
          
          SelectionButton(title: "Balanced", isSelected: selectedScalpCondition == "Balanced") {
              selectedScalpCondition = "Balanced"
          }
          
          SelectionButton(title: "Oily", isSelected: selectedScalpCondition == "Oily") {
              selectedScalpCondition = "Oily"
          }
            
          SelectionButton(title: "I'm not sure", isSelected: selectedScalpCondition == "I'm not sure") {
              selectedScalpCondition = "I'm not sure"
          }
            
         
        }
        .padding()
    }
}


#Preview {
    ScalpConditionView()
}
