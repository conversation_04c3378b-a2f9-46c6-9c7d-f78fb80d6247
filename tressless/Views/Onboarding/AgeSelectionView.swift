//
//  AgeSelectionView.swift
//  tressless
//
//  Created by <PERSON> on 20.08.2024.
//

import SwiftUI

// AgeSelectionView
struct AgeSelectionView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger
    @State private var selectedAgeRange: String?
    
    var body: some View {
        VStack(spacing: 20) {
          Image("age")
              .resizable()
              .aspectRatio(contentMode: .fit)
              .frame(height: 200)
              .padding()
          Spacer()
            Text("Select your age range")
                .font(.title)
                .padding()
            
          SelectionButton(title: "18-30", isSelected: selectedAgeRange == "18-30") {
              selectedAgeRange = "18-30"
              AnalyticsInfoLogger.shared.logEvent("Age_Range_Selected", properties: ["range": "18-30"])
          }
          
          SelectionButton(title: "31-45", isSelected: selectedAgeRange == "31-45") {
              selectedAgeRange = "31-45"
              AnalyticsInfoLogger.shared.logEvent("Age_Range_Selected", properties: ["range": "31-45"])
          }
          
          SelectionButton(title: "45+", isSelected: selectedAgeRange == "45+") {
              selectedAgeRange = "45+"
              AnalyticsInfoLogger.shared.logEvent("Age_Range_Selected", properties: ["range": "45+"])
          }
            
         
        }
        .padding()
    }
}


#Preview {
    AgeSelectionView()
}
