import SwiftUI
import FirebaseRemoteConfig

struct WelcomeView: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger
    @Environment(\.colorScheme) var colorScheme
    @ObservedObject private var remoteConfig = RemoteConfigManager.shared
    
    // Example hair analysis result
    let exampleResult = HairAnalysisResult(
        hairDensity: 63.7,
        hairLossLevel: "Moderate",
        norwoodLevel: "3",
        frontalHairCount: 82,
        occipitalHairCount: 93,
        vertexHairCount: 66
    )
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Welcome to Regrow Hair AI")
                .font(.title)
                .fontWeight(.bold)
            
            Text("Hair Loss Analysis & Tracker")
                .font(.headline)
                .multilineTextAlignment(.center)
            //if remoteConfig.showHeader {
                Image(colorScheme == .dark ? "welcome_header-dark" : "welcome_header")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(height: 120)
                    .padding()
            //}
            
            LottieView(name: "follicle")
                .frame(height: 150)
                .padding(.top, -80)
            
            /*Text("Your hair analyzed with AI")
                .font(.title3)
                .fontWeight(.semibold)*/
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 15) {
                    MetricCardView(title: "Hair Density", value: String(format: "%.1f", exampleResult.hairDensity), icon: "chart.bar.fill", color: Color(hex: 0xefd061))
                    MetricCardView(title: "Hair Loss Level", value: exampleResult.hairLossLevel, icon: "exclamationmark.triangle.fill", color: Color(hex: 0xd8cfee))
                    MetricCardView(title: "Frontal", value: "\(exampleResult.frontalHairCount)", icon: "person.fill", unit: "hair per cm²", color: Color(hex: 0xec7654))
                    MetricCardView(title: "Occipital", value: "\(exampleResult.occipitalHairCount)", icon: "person.crop.circle.dashed", unit: "hair per cm²", color: Color(hex: 0x84abc4))
                    MetricCardView(title: "Vertex", value: "\(exampleResult.vertexHairCount)", icon: "person", unit: "hair per cm²", color: Color(hex: 0x9cc3c1))
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .onAppear {
            AnalyticsInfoLogger.shared.logEvent("Welcome_View_Shown")
        }
    }
}

struct MetricCardView: View {
    let title: String
    let value: String
    let icon: String
    var unit: String? = nil
    let color: Color
    
    var body: some View {
        VStack(spacing: 10) {
            Image(systemName: icon)
                .font(.system(size: 30))
                .foregroundColor(.black)
            
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .multilineTextAlignment(.center)
                .foregroundColor(.black)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.black)
            
            if let unit = unit {
                Text(unit)
                    .font(.caption)
                    .foregroundColor(.black.opacity(0.8))
            }
        }
        .frame(width: 120, height: 120)
        .padding()
        .background(color)
        .cornerRadius(15)
    }
}

extension Color {
    init(hex: UInt, alpha: Double = 1) {
        self.init(
            .sRGB,
            red: Double((hex >> 16) & 0xff) / 255,
            green: Double((hex >> 08) & 0xff) / 255,
            blue: Double((hex >> 00) & 0xff) / 255,
            opacity: alpha
        )
    }
}


