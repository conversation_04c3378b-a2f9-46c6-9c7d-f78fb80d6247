//
//  ProgressGraphView.swift
//  tressless
//
//  Created by <PERSON> on 17.05.2025.
//

import SwiftUI

// ProgressGraphView
struct ProgressGraphView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger
    @State private var selectedAgeRange: String?
    
    var body: some View {
        VStack(spacing: 10) {
            Spacer()
            
            Text("Congratulations your AI powered hair growth journey is ready!")
                .font(.title2)
                .fontWeight(.semibold)
                .fixedSize(horizontal: false, vertical: true)
            
          Image("ProgressGraph")
              .resizable()
              .aspectRatio(contentMode: .fit)
              .frame(height: 250)
              .padding()
            
          Text("✅ AI-powered photo tracking to see real results over time")
                .font(.body)
                .fixedSize(horizontal: false, vertical: true)
                .frame(maxWidth: .infinity, alignment: .leading)
          Text("✅ Boost confidence")
                .font(.body)
                .frame(maxWidth: .infinity, alignment: .leading)
          Text("✅ See thicker hair in 90 days")
                  .font(.body)
                  .frame(maxWidth: .infinity, alignment: .leading)
          Text("✅ Access to expert-backed treatment plans tailored to your stage")
                    .font(.body)
                    .fixedSize(horizontal: false, vertical: true)
                    .frame(maxWidth: .infinity, alignment: .leading)
          Text("✅ Daily reminders to stay on track with your treatments")
                      .font(.body)
                      .frame(maxWidth: .infinity, alignment: .leading)
         
        }
        .padding()
    }
}


#Preview {
    ProgressGraphView()
}
