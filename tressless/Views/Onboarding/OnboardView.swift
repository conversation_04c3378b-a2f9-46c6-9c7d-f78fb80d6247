//
//  OnboardView.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI
import Swift<PERSON><PERSON><PERSON><PERSON>Auth

struct OnboardView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger
  @Binding var show: Bool
  @State private var currentStep = 0
  @State private var selectedEmoji: String?
  @Namespace private var animation
  @EnvironmentObject var rootState: RootState

  let onboardingSteps = ["Welcome", "Age_Selection", "Family_History_Selection", "FirstNoticeChanges", "Scalp_Condition_Selection", "FeelHairSituation", "Goals_Selection", "Customizing", "Paywall_Final", "Create_Account"]
  
  var body: some View {
      ZStack {
          Color("Background").ignoresSafeArea()
          
          VStack(spacing: 20) {
              // Progress bar removed

              TabView(selection: $currentStep) {
                  WelcomeView()
                      .tag(0)
                      .onAppear { logStep(step: onboardingSteps[0]) }
                  AgeSelectionView()
                      .tag(1)
                      .onAppear { logStep(step: onboardingSteps[1]) }
                  FamilyHistoryView()
                      .tag(2)
                      .onAppear { logStep(step: onboardingSteps[2]) }
                  FirstNoticeChangesView()
                      .tag(3)
                      .onAppear { logStep(step: onboardingSteps[3]) }
                  ScalpConditionView()
                      .tag(4)
                      .onAppear { logStep(step: onboardingSteps[4]) }
                  FeelHairSituationView()
                      .tag(5)
                      .onAppear { logStep(step: onboardingSteps[5]) }
                  GoalsView()
                      .tag(6)
                      .onAppear { logStep(step: onboardingSteps[6]) }
                  CustomizingView(currentStep: $currentStep)
                      .tag(7)
                      .onAppear { logStep(step: onboardingSteps[7]) }
                  PaywallView(currentStep: $currentStep)
                      .tag(8)
                      .onAppear { logStep(step: onboardingSteps[8]) }
                  CreateAccountView()
                      .tag(9)
                      .onAppear { logStep(step: onboardingSteps[9]) }
              }
              .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
              .animation(.easeInOut, value: currentStep)
              
              Spacer()
              
              // Next button (hide on step 8 - paywall has its own controls)
              if currentStep < 7 {
                  Button(action: {
                      withAnimation(.easeInOut(duration: 0.3)) {
                          if currentStep < 9 {
                              currentStep += 1
                              logNextStep()
                          } else {
                              show = false
                              AnalyticsInfoLogger.shared.logEvent("Onboarding_Completed")
                          }
                      }
                  }) {
                      Text("Continue")
                          .font(.headline)
                          .frame(maxWidth: .infinity)
                          .padding()
                          .foregroundColor(Color("Background"))
                          .background(Color("Background 4"))
                          .cornerRadius(10)
                  }
                  .padding(.horizontal)
                  .padding(.bottom, 5)
              }
              
              if currentStep == 0 {
                  Button(action: {
                      currentStep = 9
                  }) {
                      Text("Already have an account?")
                          .underline()
                          .font(.footnote)
                          .foregroundColor(Color("Background 4"))
                          .padding(.bottom, 10)
                  }
              }
          }
      }
      .onAppear {
          AnalyticsInfoLogger.shared.logEvent("Onboarding_Started")
      }
      .onDisappear {
          show = false
          if currentStep < 9 {
              AnalyticsInfoLogger.shared.logEvent("Onboarding_Abandoned", properties: ["step": currentStep])
          }
      }
      .onChange(of: rootState.showTabbarView) { newValue in
          if newValue {
              show = false
          }
      }
  }
  
  private func logStep(step: String) {
      AnalyticsInfoLogger.shared.logEvent("Onboarding_Step_Viewed", properties: ["step": step])
  }
  
  private func logNextStep() {
      AnalyticsInfoLogger.shared.logEvent("Onboarding_Next_Pressed", properties: ["from_step": currentStep])
  }
}

// PaywallView
struct PaywallView: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger
    @State private var isLoading = true
    @Binding var currentStep: Int
    @State private var showPayView = true

    var body: some View {
        ZStack {
            if showPayView {
                PayView(
                    onDismiss: {
                        // 🎯 NO SKIP - Paywall dismissal is not allowed, do nothing
                        AnalyticsInfoLogger.shared.logEvent("Paywall_Dismiss_Blocked")
                    },
                    onPurchaseComplete: {
                        showPayView = false
                        currentStep = 9 // Go to CreateAccountView()
                    },
                    placementId: "onboarding",
                    currentStep: $currentStep
                )
                .onAppear {
                    AnalyticsInfoLogger.shared.logEvent("Paywall_Viewed")
                    // Give some time for PayView to initialize, then set isLoading to false
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                        isLoading = false
                    }
                }
            } else {
                Color.clear // Placeholder when PayView is dismissed
            }
            
            if isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.4))
            }
        }
    }
    
    private func moveToNextStep() {
        withAnimation {
            if currentStep <= 9 { // Check if it's not the last step
                currentStep += 1
            }
        }
        AnalyticsInfoLogger.shared.logEvent("Paywall_Step_Completed")
    }
}

#Preview{
  OnboardView(show: .constant(true))
}
