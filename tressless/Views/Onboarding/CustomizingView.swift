//
//  CustomizingView.swift
//  tressless
//
//  Created by <PERSON> on 3.09.2024.
//

import SwiftUI

// Updated CustomizingView
struct CustomizingView: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger
    @Binding var currentStep: Int
    @State private var progress: Double = 0

    var body: some View {
        VStack {
          
            
          CircularCustomizingProgressBar(progress: $progress)
                .frame(width: 200, height: 200)
                .padding()
            
            Text("Please wait while we set up your personalized experience...")
                .multilineTextAlignment(.center)
                .padding()
        }
        .onAppear {
            AnalyticsInfoLogger.shared.logEvent("Customizing_View_Shown")
            startProgress()
        }
    }
    
    private func startProgress() {
        Timer.scheduledTimer(withTimeInterval: 0.03, repeats: true) { timer in
            if self.progress < 200 {
                self.progress += 1
            } else {
                timer.invalidate()
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    withAnimation {
                        currentStep += 1
                    }
                }
            }
        }
    }
}

struct CircularCustomizingProgressBar: View {
    @Binding var progress: Double
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(lineWidth: 10.0)
                .opacity(0.3)
                .foregroundColor(Color("Background 4"))
            
            Circle()
                .trim(from: 0.0, to: CGFloat(min(self.progress / 200, 1.0)))
                .stroke(style: StrokeStyle(lineWidth: 10.0, lineCap: .round, lineJoin: .round))
                .foregroundColor(Color("Background 4"))
                .rotationEffect(Angle(degrees: 270.0))
                .animation(.linear, value: progress)
            
            Text(String(format: "Customizing your experience", min(self.progress, 100.0)))
            .font(.subheadline)
                .bold()
                .foregroundColor(Color("Background 4"))
        }
    }
}


// Add preview
struct CustomizingView_Previews: PreviewProvider {
    static var previews: some View {
        CustomizingView(currentStep: .constant(0))
    }
}


