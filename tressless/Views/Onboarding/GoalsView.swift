//
//  GoalsView.swift
//  tressless
//
//  Created by <PERSON> on 20.08.2024.
//

import SwiftUI

struct GoalsView: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger
    @State private var selectedGoals: Set<String> = []
    
    let goals = ["Stop further hair loss", "Regrow thinning areas", "Understand my hair better", "Try a treatment plan", "Just exploring for now"]
    
    var body: some View {
        VStack {
            Text("What are your hair goals?")
                .font(.title)
                .padding()
            
            ForEach(goals, id: \.self) { goal in
                Button(action: {
                    if selectedGoals.contains(goal) {
                        selectedGoals.remove(goal)
                    } else {
                        selectedGoals.insert(goal)
                    }
                    AnalyticsInfoLogger.shared.logEvent("Goal_Selected", properties: ["goal": goal])
                }) {
                    HStack {
                        Text(goal)
                        Spacer()
                        if selectedGoals.contains(goal) {
                            Image(systemName: "checkmark")
                        }
                    }
                }
                .padding()
                .background(selectedGoals.contains(goal) ? Color.black : Color("Background 4").opacity(0.1))
                .foregroundColor(selectedGoals.contains(goal) ? Color.white : Color("Background 4"))
                .cornerRadius(8)
            }
        }
        .padding()
        .onAppear {
            AnalyticsInfoLogger.shared.logEvent("Goals_Selection_Viewed")
        }
    }
}


