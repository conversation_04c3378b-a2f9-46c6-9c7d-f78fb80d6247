import SwiftUI
import StoreKit

struct EmojiButton: View {
  
    let emoji: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(emoji)
                .font(.system(size: 40))
                .padding()
                .frame(width: 80, height: 80)
                .background(isSelected ? Color.blue.opacity(0.2) : Color.white)
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(isSelected ? Color.blue : Color.gray, lineWidth: 2)
                )
        }
    }
}

struct ReviewView: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger
    @Environment(\.requestReview) var requestReview
    @Environment(\.colorScheme) var colorScheme
    @Binding var currentStep: Int
    
    var body: some View {
        VStack(spacing: 20) {
            Image(colorScheme == .dark ? "lovedbythousands-dark" : "lovedbythousands")
                .resizable()
                .scaledToFit()
                .frame(height: 150) // Adjust the height as needed
                .padding(.top)
            
            Text("We'd love your feedback!")
                .font(.title)
                .padding()
            
            Text("Your review helps us improve the app.")
                .font(.headline)
                .multilineTextAlignment(.center)
                .padding()
            
            Spacer()
            
            Image("review")
                .resizable()
                .scaledToFit()
                .frame(height: 100) // Adjust the height as needed
            
            Spacer()
            
            Button(action: {
                requestReview()
                AnalyticsInfoLogger.shared.logEvent("App_Store_Review_Requested")
                // Progress to the next step
                withAnimation {
                    currentStep += 1
                }
            }) {
                Text("Leave a Review")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .foregroundColor(Color("Background"))
                    .background(Color("Background 4"))
                    .cornerRadius(10)
            }
            .padding(.horizontal)
        }
        .padding()
        .onAppear {
            AnalyticsInfoLogger.shared.logEvent("Review_View_Shown")
        }
    }
}
