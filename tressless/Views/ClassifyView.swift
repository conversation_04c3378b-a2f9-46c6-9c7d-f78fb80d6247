//
//  ClassifyView.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI
import UIKit
import FirebaseFirestore
import FirebaseAuth
import Alamofire
import RevenueCat
import RevenueCatUI
import FirebaseRemoteConfig
import Lottie
import FirebaseStorage // Add this import at the top of the file

struct HairAnalysisResult {
    let hairDensity: Double
    let hairLossLevel: String
    let norwoodLevel: String
    let frontalHairCount: Double
    let occipitalHairCount: Double
    let vertexHairCount: Double
}


struct ClassifyView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger
  @EnvironmentObject var root: RootState
  @Environment(\.dismiss) private var dismiss
  
  @State private var selectedImage: UIImage?
  @State private var isImagePickerPresented = false
  @StateObject private var networkManager: NetworkManager

  @State private var isScanning: Bool = false
  @State private var isAnalysisViewPresented = false
  @State private var analysisResult: HairAnalysisResult?
  @State private var showError = false
  @State private var errorMessage: String?
  @State private var showErrorAlert = false
  @State private var apiAnalysisResult: APIHairAnalysisResult?
  @State private var analysisCompleted = false
  @Environment(\.presentationMode) var presentationMode

  @State private var navigateToResults = false
  @State private var showPaywall = false

  @State private var confidenceThreshold: Double = 0.5 // Default value

  @State private var analysesLeft: Int = 0 // Add this state variable to track analyses left

  @State private var showDiscountAlert = false
  @State private var showDiscountedPaywall = false

  @State private var showLockedResults = false

  var onCompletion: (Bool) -> Void

  init(onCompletion: @escaping (Bool) -> Void) {
    self.onCompletion = onCompletion
    _networkManager = StateObject(wrappedValue: NetworkManager())
  }

  var body: some View {
    NavigationView {
      VStack {
        Text("AI Hair Density Analyze")
          .font(.custom("KdamThmorPro-Regular", size: 28))
        
        if let selectedImage = selectedImage {
          ZStack {
            Image(uiImage: selectedImage)
              .resizable()
              .scaledToFit()
              .cornerRadius(10)
              .frame(maxHeight: 300)
              .padding()
            
            if isScanning {
              LottieView(name: "scan")
                .frame(width: 400, height: 400)
            }
            
            Button(action: {
              AnalyticsInfoLogger.shared.logEvent("Image_Upload_Cleared")  // Event: User cleared the uploaded image
              self.selectedImage = nil
              self.analysisResult = nil
              self.apiAnalysisResult = nil // Reset when a new image is selected
              
            }) {
              Image(systemName: "xmark.circle.fill")
                .foregroundColor(.white)
                .background(Color.black.opacity(0.7))
                .clipShape(Circle())
            }
            .padding([.top, .trailing], 10)
            .position(x: UIScreen.main.bounds.width - 40, y: 20)
          }
        } else {
          ImageGuideView()
        }
        
        VStack {
          Text("Detailed Analysis")
            .font(.headline)
          Text("Our advanced AI technology will analyze your hair image to provide detailed insights into your hair health, including density and potential issues.")
            .font(.subheadline)
            .foregroundColor(.gray)
        }
        
        Button("Select Image") {
          AnalyticsInfoLogger.shared.logEvent("Image_Selection_Started")  // Event: User started selecting an image
          isImagePickerPresented = true
          
        }
        .padding(.top, 25)
        .disabled(isScanning)
        .padding(.bottom, selectedImage == nil ? 66 : 0) // Add bottom padding only if no image is selected
        .onChange(of: selectedImage) { newImage in
          if newImage != nil {
            AnalyticsInfoLogger.shared.logEvent("Image_Selected", properties: ["imageSource": "Photo Library"])  // Event: Image selected
            apiAnalysisResult = nil // Reset the results when a new image is selected
          }
        }
        
        
        
        // Analyze Hair Button
        if selectedImage != nil {
          Button(action: {
            AnalyticsInfoLogger.shared.logEvent("Hair_Analysis_Started")  // Event: Hair analysis process started
            analyzeHair()
          }) {
            Text("Analyze Hair")
              .font(.headline)
              .frame(maxWidth: .infinity)
              .padding()
              .background(isScanning || selectedImage == nil ? Color.blue.opacity(0.5) : Color.blue)
              .foregroundColor(.white)
              .cornerRadius(10)
          }
          
          .disabled(isScanning || selectedImage == nil)
          .padding(.bottom, apiAnalysisResult == nil ? 66 : 0) // Add bottom padding only if no image is selected
          
        }
        
        // Show Results Button
        if selectedImage != nil && apiAnalysisResult != nil {
          Button(action: {
            AnalyticsInfoLogger.shared.logEvent("Show_Results_Button_Pressed")  // Event: User pressed Show Results
            self.handleShowResults()
          }) {
            Text("Show Results")
              .font(.headline)
              .frame(maxWidth: .infinity)
              .padding()
              .background(Color.clear)
              .cornerRadius(10)
              .overlay(
                RoundedRectangle(cornerRadius: 10)
                  .stroke(Color.blue, lineWidth: 2)
              )
              .foregroundColor(.blue)
          }
          
          .padding(.bottom, 60)
          
        }
        
        NavigationLink(destination: ResultsView(result: analysisResult ?? HairAnalysisResult(hairDensity: 0, hairLossLevel: "N/A", norwoodLevel: "N/A", frontalHairCount: 0, occipitalHairCount: 0, vertexHairCount: 0)), isActive: $navigateToResults) {
            EmptyView()
        }
      }
      .padding()
      .alert(isPresented: $showError) {
        Alert(title: Text("Error"), message: Text(errorMessage ?? "Unknown error"), dismissButton: .default(Text("OK")))
      }
     
      .sheet(isPresented: $showPaywall) {
        PayView(
          onDismiss: {
            showPaywall = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
              showDiscountAlert = true
            }
          },
          onPurchaseComplete: {
            showPaywall = false
            self.updateAnalysisAccessibility()
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                if let updatedResult = self.apiAnalysisResult {
                    self.analysisResult = self.convertToViewResult(apiResult: updatedResult)
                    self.navigateToResults = true
                } else {
                    print("Error: apiAnalysisResult is nil after purchase")
                }
            }
          },
          placementId: "results_view",
          currentStep: .constant(0)
        )
      }

      // Add a new sheet for the discounted PayView
      .sheet(isPresented: $showDiscountedPaywall) {
        PayView(
          onDismiss: {
            showDiscountedPaywall = false
          },
          onPurchaseComplete: {
            showDiscountedPaywall = false
            self.updateAnalysisAccessibility()
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                if let updatedResult = self.apiAnalysisResult {
                    self.analysisResult = self.convertToViewResult(apiResult: updatedResult)
                    self.navigateToResults = true
                } else {
                    print("Error: apiAnalysisResult is nil after purchase")
                }
            }
          },
          placementId: "discounted",
          currentStep: .constant(0)
        )
      }

      // Add an alert for the discount offer
      .alert(isPresented: $showDiscountAlert) {
        Alert(
          title: Text("Special Offer!"),
          message: Text("It seems the price is high for you. Would you like to get 50% off?"),
          primaryButton: .default(Text("Get Offer")) {
            showDiscountedPaywall = true
          },
          secondaryButton: .cancel()
        )
      }

      // Add this new sheet presentation for LockedResultsView
      .sheet(isPresented: $showLockedResults) {
        LockedResultsView(onContinuePressed: {
          showLockedResults = false
          // Show paywall after a brief delay
          DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            showPaywall = true
          }
        })
      }
    }
    .sheet(isPresented: $isImagePickerPresented) {
      ImagePicker(selectedImage: self.$selectedImage)
    }
    .onAppear {
      AnalyticsInfoLogger.shared.logEvent("Classify_View_Opened")
      loadConfidenceThreshold()
      // Load analysesLeft when the view appears
      self.loadAnalysesLeft()
    }
    .onDisappear {
      onCompletion(apiAnalysisResult != nil)
    }
  }
  
  func analyzeHair() {
    guard let image = selectedImage else { return }
    isScanning = true
    
    networkManager.analyzeHair(image: image) { result in
        DispatchQueue.main.asyncAfter(deadline: .now() + 10) { // Ensure at least 10 seconds delay
            DispatchQueue.main.async {
                self.isScanning = false
                print(result)
                switch result {
                case .success(let analysisResult):
                    if let error = analysisResult.error {
                        self.showError(message: error)
                        AnalyticsInfoLogger.shared.logEvent("Hair_Analysis_Error", properties: ["error": error])
                    } else if analysisResult.segmentation_confidence < self.confidenceThreshold {
                        let errorMessage = "Segmentation confidence too low. Please try a different image."
                        self.showError(message: errorMessage)
                        AnalyticsInfoLogger.shared.logEvent("Hair_Analysis_Error", properties: ["error": errorMessage])
                    } else {
                        self.apiAnalysisResult = analysisResult
                        
                        // Check analysesLeft before saving and processing
                        self.checkUserAnalysisLimit { hasAnalysisLeft in
                            if hasAnalysisLeft {
                                self.saveHairDensityToFirebase(result: analysisResult)
                                self.decrementUserAnalysisCount()
                                self.analysisResult = self.convertToViewResult(apiResult: analysisResult)
                                self.navigateToResults = true
                                self.analysisCompleted = true
                                self.onCompletion(true)
                            } else {
                                // Save the analysis as inaccessible
                                var inaccessibleResult = analysisResult
                                inaccessibleResult.isAccessible = false
                                self.saveHairDensityToFirebase(result: inaccessibleResult)
                                // Show LockedResultsView instead of paywall
                                DispatchQueue.main.async {
                                    self.showLockedResults = true
                                }
                            }
                        }
                    }
                case .failure(let error):
                    self.showError(message: error.localizedDescription)
                    AnalyticsInfoLogger.shared.logEvent("Hair_Analysis_Error", properties: ["error": error.localizedDescription])
                }
            }
        }
    }
  }
  
  private func showError(message: String) {
    self.errorMessage = message
    self.showError = true
  }
  
  func convertToViewResult(apiResult: APIHairAnalysisResult?) -> HairAnalysisResult {
    guard let result = apiResult else {
      return HairAnalysisResult(hairDensity: 0, hairLossLevel: "N/A", norwoodLevel: "N/A", frontalHairCount: 0, occipitalHairCount: 0, vertexHairCount: 0)
    }
    
    return HairAnalysisResult(
      hairDensity: result.hair_density,
      hairLossLevel: result.hairLossLevel,
      norwoodLevel: result.norwoodLevel,
      frontalHairCount: result.haircountperarea["Frontal scalp"] ?? 0,
      occipitalHairCount: result.haircountperarea["Occipital scalp"] ?? 0,
      vertexHairCount: result.haircountperarea["Vertex"] ?? 0
    )
  }
  
  func saveHairDensityToFirebase(result: APIHairAnalysisResult) {
    guard let userId = Auth.auth().currentUser?.uid else { return }
    guard let imageData = selectedImage?.jpegData(compressionQuality: 0.8) else { return }
    
    checkUserAnalysisLimit { hasAnalysisLeft in
        let db = Firestore.firestore()
        let storage = Storage.storage()
        let storageRef = storage.reference()
        
        // Create a unique filename for the image
        let imageName = "\(UUID().uuidString).jpg"
        let imageRef = storageRef.child("hair_analyses/\(userId)/\(imageName)")
        
        // Upload the image to Firebase Storage
        let uploadTask = imageRef.putData(imageData, metadata: nil) { (metadata, error) in
            if let error = error {
                print("Error uploading image: \(error.localizedDescription)")
                return
            }
            
            // Get the download URL for the uploaded image
            imageRef.downloadURL { (url, error) in
                if let error = error {
                    print("Error getting download URL: \(error.localizedDescription)")
                    return
                }
                
                guard let downloadURL = url else { return }
                
                // Now save the analysis data along with the image URL to Firestore
                let data: [String: Any] = [
                    "finalHairDensity": result.hair_density,
                    "hairLossLevel": result.hairLossLevel,
                    "norwoodLevel": result.norwoodLevel,
                    "timestamp": Timestamp(date: Date()),
                    "hairCountsPerArea": result.haircountperarea,
                    "isAccessible": hasAnalysisLeft,
                    "imageUrl": downloadURL.absoluteString // Add the image URL to the document
                ]
                
                db.collection("users").document(userId).collection("hair_density_analyses").addDocument(data: data) { error in
                    if let error = error {
                        print("Error saving hair density: \(error.localizedDescription)")
                    } else {
                        print("Hair density and image successfully saved.")
                        
                        AnalyticsInfoLogger.shared.logEvent("Hair_Density_Analysis_Saved", properties: [
                            "finalHairDensity": result.hair_density,
                            "hairLossLevel": result.hairLossLevel,
                            "norwoodLevel": result.norwoodLevel,
                            "isAccessible": hasAnalysisLeft,
                            "hasImage": true
                        ])
                    }
                }
            }
        }
        
        // Observe the upload progress if needed
        uploadTask.observe(.progress) { snapshot in
            let percentComplete = 100.0 * Double(snapshot.progress!.completedUnitCount) / Double(snapshot.progress!.totalUnitCount)
            print("Upload is \(percentComplete)% complete")
        }
    }
  }
  
  // New Function to Handle Show Results Action
  func handleShowResults() {
    checkUserAnalysisLimit { hasAnalysisLeft in
        DispatchQueue.main.async {
            if hasAnalysisLeft {
                self.analysisResult = self.convertToViewResult(apiResult: apiAnalysisResult)
                self.navigateToResults = true
                self.analysisCompleted = true
                self.onCompletion(true)
            } else {
                // Show LockedResultsView instead of paywall directly
                self.showLockedResults = true
            }
        }
    }
  }
  
  // New Function to Load Analyses Left
  func loadAnalysesLeft() {
      checkUserAnalysisLimit { hasAnalysisLeft in
          // Assuming `checkUserAnalysisLimit` can be modified to return the actual count
          // Otherwise, you might need to create a new function to fetch the count
          // For demonstration, we'll set `analysesLeft` based on the boolean
          DispatchQueue.main.async {
              self.analysesLeft = hasAnalysisLeft ? 1 : 0 // Replace with actual count if available
          }
      }
  }
  
  // Sync subscription status before checking analysis limit (like main2)
  func checkUserAnalysisLimit(completion: @escaping (Bool) -> Void) {
      guard let userId = Auth.auth().currentUser?.uid else {
          completion(false)
          return
      }

      // First sync with RevenueCat, then check Firebase
      Task {
          do {
              let customerInfo = try await Purchases.shared.customerInfo()
              let isPremium = customerInfo.entitlements["premium"]?.isActive ?? false

              // Update Firebase with current RevenueCat status
              await syncSubscriptionStatus(userId: userId, isPremium: isPremium)

              // Then read from Firebase for analysis limit
              await MainActor.run {
                  readAnalysisLimitFromFirebase(userId: userId, completion: completion)
              }

          } catch {
              print("Failed to sync subscription status: \(error)")
              // Fallback to reading from Firebase
              readAnalysisLimitFromFirebase(userId: userId, completion: completion)
          }
      }
  }

  private func readAnalysisLimitFromFirebase(userId: String, completion: @escaping (Bool) -> Void) {
      let db = Firestore.firestore()
      let userDocRef = db.collection("users").document(userId).collection("subscriptions").document("current_subscription")

      userDocRef.getDocument { document, error in
          if let document = document, document.exists, let data = document.data() {
              let analyzesLeft = data["analyzesLeft"] as? Int ?? 0
              self.analysesLeft = analyzesLeft // Update the state variable
              completion(analyzesLeft > 0)
          } else {
              self.analysesLeft = 0
              completion(false)
          }
      }
  }

  // Sync subscription status with Firebase (matches main2 approach)
  @MainActor
  private func syncSubscriptionStatus(userId: String, isPremium: Bool) async {
      let db = Firestore.firestore()
      let subscriptionRef = db.collection("users").document(userId)
          .collection("subscriptions").document("current_subscription")

      let subscriptionData: [String: Any] = [
          "subscriptionType": isPremium ? "premium" : "free",
          "analyzesLeft": isPremium ? 1000 : 0,
          "lastChecked": Timestamp(date: Date())
      ]

      do {
          try await subscriptionRef.setData(subscriptionData, merge: true)
          print("Subscription status synced from ClassifyView: \(isPremium ? "premium" : "free")")
      } catch {
          print("Failed to sync subscription status: \(error.localizedDescription)")
      }
  }
  
  func decrementUserAnalysisCount() {
    guard let userId = Auth.auth().currentUser?.uid else { return }
    
    let db = Firestore.firestore()
    let userDocRef = db.collection("users").document(userId).collection("subscriptions").document("current_subscription")
    
    userDocRef.updateData([
        "analyzesLeft": FieldValue.increment(Int64(-1)), // This decrements by 1
        "analysesUsed": FieldValue.increment(Int64(1))   // This increments by 1
    ]) { error in
        if let error = error {
            print("Error decrementing analyses left: \(error.localizedDescription)")
        } else {
            print("Successfully decremented analyses left")
            // Update the local state
            DispatchQueue.main.async {
                self.analysesLeft -= 1
            }
        }
    }
  }

  // Add this new function to update the analysis accessibility
  func updateAnalysisAccessibility() {
    guard let userId = Auth.auth().currentUser?.uid else { return }
    
    let db = Firestore.firestore()
    db.collection("users").document(userId).collection("hair_density_analyses")
        .order(by: "timestamp", descending: true)
        .limit(to: 1)
        .getDocuments { (querySnapshot, error) in
            if let error = error {
                print("Error getting documents: \(error)")
            } else if let document = querySnapshot?.documents.first {
                document.reference.updateData([
                    "isAccessible": true
                ]) { err in
                    if let err = err {
                        print("Error updating document: \(err)")
                    } else {
                        print("Document successfully updated to be accessible")
                        // Update the local apiAnalysisResult to reflect the change
                        if var updatedResult = self.apiAnalysisResult {
                            updatedResult.isAccessible = true
                            self.apiAnalysisResult = updatedResult
                        }
                        
                        // Force a refresh of the UI
                        DispatchQueue.main.async {
                            self.analysisCompleted = true
                        }
                        
                        // Log the update
                        print("Updated most recent analysis to be accessible")
                    }
                }
            } else {
                print("No documents found to update")
            }
        }
  }
}

private func clearAppStorage() {
    // Example of clearing specific AppStorage values
    UserDefaults.standard.removeObject(forKey: "someKey")
    UserDefaults.standard.removeObject(forKey: "anotherKey")
    // Clear all stored keys
    if let appDomain = Bundle.main.bundleIdentifier {
        UserDefaults.standard.removePersistentDomain(forName: appDomain)
    }
    UserDefaults.standard.synchronize()
}

private func clearCache() {
    // Clear URLCache
    URLCache.shared.removeAllCachedResponses()
    
    // Clear temporary files
    let tempDirectory = FileManager.default.temporaryDirectory
    do {
        let tempFiles = try FileManager.default.contentsOfDirectory(atPath: tempDirectory.path)
        for file in tempFiles {
            let filePath = tempDirectory.appendingPathComponent(file).path
            try FileManager.default.removeItem(atPath: filePath)
        }
    } catch {
        print("Error clearing temporary files: \(error.localizedDescription)")
    }
}






#Preview {
    ClassifyView(onCompletion: { _ in })
}

// Add this extension at the end of the file
extension ClassifyView {
    func loadConfidenceThreshold() {
        self.confidenceThreshold = RemoteConfigManager.shared.getDouble(forKey: "segmentation_confidence_threshold")
    }
}



















