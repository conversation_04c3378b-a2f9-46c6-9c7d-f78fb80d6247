//
//  PastAnalysesView.swift
//  tressless
//
//  Created by <PERSON> on 26.09.2024.
//

import SwiftUI
import FirebaseAuth
import FirebaseFirestore

struct PastAnalysesView: View {
    @Environment(\.dismiss) var dismiss
    @State private var analyses: [PastAnalysis] = []
    @State private var isLoading = true
    @State private var errorMessage: String?

    var body: some View {
        NavigationView {
            Group {
                if isLoading {
                    ProgressView()
                } else if let error = errorMessage {
                    Text("Error: \(error)")
                } else if analyses.isEmpty {
                    Text("No past analyses found")
                } else {
                    List(analyses) { analysis in
                        NavigationLink(destination: PastAnalysisResultsView(analysis: analysis)) {
                            PastAnalysisRow(analysis: analysis)
                        }
                        .listRowInsets(EdgeInsets())
                        .padding(.vertical, 4)
                        .listRowBackground(Color.clear)
                    }
                    .listStyle(.plain)
                }
            }
            .navigationTitle("Past Analyses")
            .onAppear {
                fetchAnalyses()
            }
        }
    }

    private func fetchAnalyses() {
        guard let userId = Auth.auth().currentUser?.uid else {
            self.isLoading = false
            self.errorMessage = "User not authenticated"
            return
        }

        let db = Firestore.firestore()
        db.collection("users").document(userId).collection("hair_density_analyses")
            .whereField("isAccessible", isEqualTo: true)
            .getDocuments { (querySnapshot, error) in
                self.isLoading = false
                if let error = error {
                    self.errorMessage = "Error getting documents: \(error.localizedDescription)"
                    print(self.errorMessage!)
                } else {
                    guard let documents = querySnapshot?.documents else {
                        self.errorMessage = "No documents found"
                        return
                    }
                    
                    self.analyses = documents.compactMap { document in
                        do {
                            let analysis = try document.data(as: PastAnalysis.self)
                            print("Fetched analysis: \(analysis)") // Debugging statement
                            return analysis
                        } catch {
                            print("Error decoding document \(document.documentID): \(error)")
                            return nil
                        }
                    }
                    
                    // Sort the analyses by date after fetching
                    self.analyses.sort { $0.date > $1.date }
                    
                    if self.analyses.isEmpty {
                        self.errorMessage = "No valid analyses found"
                    }
                    
                    print("Total analyses fetched: \(self.analyses.count)") // Debugging statement
                    // Print all analyses to verify uniqueness
                    for analysis in self.analyses {
                        print("Analysis Date: \(analysis.date), Hair Density: \(analysis.hairDensity)")
                    }
                }
            }
    }
}

struct PastAnalysisRow: View {
    let analysis: PastAnalysis
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(analysis.date.toDateString())
                    .font(.headline)
                    .foregroundColor(Color("Background 2"))
                Text("Hair Density: \(analysis.hairDensity, specifier: "%.2f")")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                Text("Hair Loss Level: \(analysis.hairLossLevel)")
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            Spacer()
        }
        .padding()
        .background(Color(UIColor(hexString: "#FCEBC4")))
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct PastAnalysisResultsView: View {
    var analysis: PastAnalysis

    var body: some View {
        ZStack {
            VStack(alignment: .leading) {
                // Title and subtitle
                Text("Analysis Result")
                    .font(.title3)
                    .fontWeight(.semibold)
                Text("Your past analysis results provide a comprehensive overview of your hair health.")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                Spacer()
                // Key Metrics
                VStack(alignment: .leading) {
                    Text("Key Metrics")
                        .font(.headline)
                    MetricView(value: "\(analysis.hairDensity)", title: "Hair Density")
                    HStack {
                        MetricView(label: analysis.hairLossLevel, icon: "exclamationmark.triangle.fill", title: "Hair Loss Level")
                        MetricView(label: "Level \(analysis.norwoodLevel)", icon: "chart.bar.fill", title: "Norwood Level")
                    }
                }
                Spacer()
                // Scalp Hair Counts
                VStack {
                    Text("Scalp Hair Counts")
                        .font(.headline)
                    Text("±10%")
                        .font(.footnote)
                        .fontWeight(.ultraLight)
                    HStack {
                        CountView(label: "Frontal", count: analysis.frontalHairCount)
                        Spacer()
                        CountView(label: "Occipital", count: analysis.occipitalHairCount)
                        Spacer()
                        CountView(label: "Vertex", count: analysis.vertexHairCount)
                    }
                }
                Spacer()
                // Detailed Analysis
                VStack {
                    Text("Detailed Analysis")
                        .font(.headline)
                    Text("The Hair Loss Level indicates a \(analysis.hairLossLevel.lowercased()) level of hair loss, which corresponds to Level \(analysis.norwoodLevel) on the Norwood scale. The Final Hair Density is measured at \(analysis.hairDensity, specifier: "%.2f")%, which is a crucial indicator of overall hair health.")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
            }
            .padding()
            .scrollContentBackground(.hidden)
            .navigationBarTitle("Analysis Details", displayMode: .inline)
        }
    }
}

extension Date {
    func toDateString() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: self)
    }
}

#Preview {
    PastAnalysesView()
}
