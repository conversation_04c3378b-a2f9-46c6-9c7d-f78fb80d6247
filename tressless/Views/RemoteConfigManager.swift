//
//  RemoteConfigManager.swift
//  tressless
//
//  Created by <PERSON> on 11.09.2024.
//

import FirebaseRemoteConfig
import SwiftUI

class RemoteConfigManager: ObservableObject {
    static let shared = RemoteConfigManager()
    private let remoteConfig: RemoteConfig
    
    @Published var showHeader: Bool = false
    
    private init() {
        remoteConfig = RemoteConfig.remoteConfig()
        let settings = RemoteConfigSettings()
        settings.minimumFetchInterval = 0 // For development, set to a higher value in production
        remoteConfig.configSettings = settings
        
        // Set default values
        remoteConfig.setDefaults([
            "showHeader": false as NSObject
        ])
    }
    
    func fetchConfig(completion: @escaping (Error?) -> Void) {
        remoteConfig.fetch { [weak self] status, error in
            if status == .success {
                self?.remoteConfig.activate { _, error in
                    DispatchQueue.main.async {
                        self?.updateValues()
                    }
                    completion(error)
                }
            } else {
                completion(error)
            }
        }
    }
    
    private func updateValues() {
        self.showHeader = remoteConfig.configValue(forKey: "showHeader").boolValue
    }
    
    func getString(forKey key: String) -> String {
        return remoteConfig.configValue(forKey: key).stringValue ?? ""
    }
    
    func getDouble(forKey key: String) -> Double {
        return remoteConfig.configValue(forKey: key).numberValue.doubleValue
    }
}
