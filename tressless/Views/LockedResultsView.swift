//
//  LockedResultsView.swift
//  tressless
//
//  Created by <PERSON> on 21.01.2025.
//

import SwiftUI

struct LockedResultsView: View {
    @Environment(\.dismiss) var dismiss
    @Environment(\.presentationMode) var presentationMode
    var onContinuePressed: () -> Void
    
    // Using dummy values for layout
    let dummyResult = HairAnalysisResult(
        hairDensity: 0,
        hairLossLevel: "Locked",
        norwoodLevel: "0",
        frontalHairCount: 0,
        occipitalHairCount: 0,
        vertexHairCount: 0
    )

    var body: some View {
        ZStack {
            VStack(alignment: .leading) {
                // Marketing Call-to-Action
                VStack(spacing: 8) {
                    Text("Everything is ready 👀")
                        .font(.title)
                        .fontWeight(.bold)
                  
                    
                }
                .frame(maxWidth: .infinity)
                .padding(.bottom, 20)
                
                // Title and subtitle
                Text("Hair Analysis Results")
                    .font(.title3)
                    .fontWeight(.semibold)
                Text("Your hair analysis results provide a comprehensive overview of your hair health. ")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                Spacer()
                
                // Key Metrics
                VStack(alignment: .leading) {
                    Text("Key Metrics")
                        .font(.headline)
                    
                    // Locked Hair Density
                    LockedMetricView(title: "Hair Density")
                    
                    HStack {
                        LockedMetricView(title: "Hair Loss Level")
                        LockedMetricView(title: "Norwood Level")
                    }
                }
                
                Spacer()
                
                // Scalp Hair Counts
                VStack {
                    Text("Scalp Hair Counts")
                        .font(.headline)
                    Text("±10%")
                        .font(.footnote)
                        .fontWeight(.ultraLight)
                    HStack {
                        LockedCountView(label: "Frontal")
                        Spacer()
                        LockedCountView(label: "Occipital")
                        Spacer()
                        LockedCountView(label: "Vertex")
                    }
                }
                
                Spacer()
                
             
                
                // Add Continue button at the bottom
                Button(action: {
                    onContinuePressed()
                }) {
                    Text("Continue")
                        .font(.headline)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .padding(.top, 20)
            }
            .padding()
            .scrollContentBackground(.hidden)
            .navigationBarTitle("Hair Analyze", displayMode: .inline)
        }
    }
}

struct LockedMetricView: View {
    var title: String
    
    var body: some View {
        VStack {
            Text(title)
            .font(.subheadline)
            HStack {
                Text("🔒")
                    .font(.title2)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .foregroundColor(Color.white)
        .background(Color(red: 0.165, green: 0.173, blue: 0.176))
        .cornerRadius(10)
    }
}

struct LockedCountView: View {
    var label: String
    
    var body: some View {
        VStack {
            VStack {
                Text("🔒")
                    .font(.title2)
                Text("per cm²")
                    .font(.footnote)
                    .fontWeight(.thin)
            }
            
            Text(label)
                .font(.subheadline)
                .fontWeight(.medium)
        }
        .font(.headline)
        .frame(maxWidth: .infinity)
        .padding()
        .foregroundColor(Color.white)
        .background(Color(red: 0.165, green: 0.173, blue: 0.176))
        .cornerRadius(10)
    }
}


