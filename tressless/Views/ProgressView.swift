//
//  ProgressView.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//
import SwiftUI
import PhotosUI
import FirebaseStorage
import FirebaseFirestore
import FirebaseAuth


enum Area: String, CaseIterable {
    case front = "Front"
    case back = "Back"
    case top = "Top"
    case right = "Right"
    case left = "Left"
}

class PhotoSelectorViewModel: ObservableObject {
    @Published var images = [UIImage]()
    @Published var selectedPhotos = [PhotosPickerItem]()
    @Published var uploadProgress = 0.0
    
    private let storageRef = Storage.storage().reference()
    private let db = Firestore.firestore()
    private let frontImageService: ImageService
    private let backImageService: ImageService
    private let topImageService: ImageService
    private let rightImageService: ImageService
    private let leftImageService: ImageService

    private let fileManager = LocalFileManager.instance
    private let folderName = "hair_progress_images"

    init(frontImageService: ImageService, backImageService: ImageService, topImageService: ImageService, rightImageService: ImageService, leftImageService: ImageService) {
        self.frontImageService = frontImageService
        self.backImageService = backImageService
        self.topImageService = topImageService
        self.rightImageService = rightImageService
        self.leftImageService = leftImageService
    }
    @MainActor
    func convertDataToImage() {
        images.removeAll()
        
        if !selectedPhotos.isEmpty {
            for eachItem in selectedPhotos {
                Task {
                    if let imageData = try? await eachItem.loadTransferable(type: Data.self) {
                        if let image = UIImage(data: imageData) {
                            images.append(image)
                        }
                    }
                }
            }
        }
        selectedPhotos.removeAll()
    }

    func uploadImages(area: Area) {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        // Create a reference for each image with unique name
        for (index, image) in images.enumerated() {
            let imageName = UUID().uuidString + ".jpg"
            let imageRef = storageRef.child("hair_treatment/\(area.rawValue)/\(imageName)")
            
            // Upload the image
            guard let imageData = image.jpegData(compressionQuality: 0.8) else { return }
            let uploadTask = imageRef.putData(imageData)
            
            // Observe progress and completion
            uploadTask.observe(.progress) { snapshot in
                self.uploadProgress = Double(snapshot.progress!.completedUnitCount) / Double(snapshot.progress!.totalUnitCount)
            }
            
            uploadTask.observe(.success) { _ in
                // Get the download URL after upload
                imageRef.downloadURL { url, error in
                    guard let downloadURL = url else { return }
                    
                    // Save image data to Firestore
                    let imageData = ["area": area.rawValue, "url": downloadURL.absoluteString]
                    self.db.collection("users").document(userId).collection("hair_treatment_photos").addDocument(data: imageData) { error in
                        if let error = error {
                            print("Error saving image data: \(error.localizedDescription)")
                        } else {
                            print("Image data saved successfully!")
                            
                            // Cache the images
                            self.cacheImages(self.images, for: area)
                            
                            // Notify the corresponding ImageService instance to update its images
                            self.updateImageService(for: area)
                        }
                    }
                }
            }
        }
    }

    func loadCachedImages(for area: Area) -> [UIImage] {
        var cachedImages: [UIImage] = []
        for index in 0..<10 { // Assuming a maximum of 10 images per area
            let imageName = "\(area.rawValue)_\(index)"
            if let image = fileManager.getImage(imageName: imageName, folderName: folderName) {
                cachedImages.append(image)
            }
        }
        return cachedImages
    }

    func cacheImages(_ images: [UIImage], for area: Area) {
        for (index, image) in images.enumerated() {
            let imageName = "\(area.rawValue)_\(index)"
            fileManager.saveImage(image: image, imageName: imageName, folderName: folderName)
        }
    }

    private func updateImageService(for area: Area) {
        switch area {
        case .front:
            frontImageService.images = loadCachedImages(for: .front)
        case .back:
            backImageService.images = loadCachedImages(for: .back)
        case .top:
            topImageService.images = loadCachedImages(for: .top)
        case .right:
            rightImageService.images = loadCachedImages(for: .right)
        case .left:
            leftImageService.images = loadCachedImages(for: .left)
        }
    }

}

struct HairProgressView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger

    @ObservedObject var vm: PhotoSelectorViewModel
       let maxPhotosToSelect = 10
       
       @State private var selectedArea: Area = .front
       @State private var showImageSelectionModal = false
       @State private var currentIndex: Double = 0
       @State private var imageWidth: CGFloat = 300
       
       @State private var scrollProxy: ScrollViewProxy? = nil
       
       private let frontImageService = ImageService(area: .front)
       private let backImageService = ImageService(area: .back)
       private let topImageService = ImageService(area: .top)
       private let rightImageService = ImageService(area: .right)
       private let leftImageService = ImageService(area: .left)
       
       @State private var selectedItem: Item = Item(id: "front", name: "Front")
       private var items: [Item] {
           let frontItem = Item(id: "front", name: "Front")
           let backItem = Item(id: "back", name: "Back")
           let topItem = Item(id: "top", name: "Top")
           let rightItem = Item(id: "right", name: "Right")
           let leftItem = Item(id: "left", name: "Left")
           return [frontItem, backItem, topItem, rightItem, leftItem]
       }

       init() {
           let viewModel = PhotoSelectorViewModel(
               frontImageService: frontImageService,
               backImageService: backImageService,
               topImageService: topImageService,
               rightImageService: rightImageService,
               leftImageService: leftImageService
           )
           _vm = ObservedObject(wrappedValue: viewModel)
       }
    var body: some View {
        VStack {
            // Area Selector
            CustomPicker(items: items, selection: $selectedItem) { item in
                VStack {
                    Image(item.id)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: 40)
                    Text(item.name)
                        .font(.footnote)
                }
            }
            .onAppear {
                       AnalyticsInfoLogger.shared.logEvent("Hair_Progress_View_Opened")
                       loadImagesForCurrentArea()
                   }
            .onChange(of: selectedItem) { item in
                selectedArea = Area.allCases.first(where: { $0.rawValue == item.name }) ?? .front
                currentIndex = 0
              AnalyticsInfoLogger.shared.logEvent("Area_Changed", properties: ["area": selectedArea.rawValue])
                loadImagesForCurrentArea()
            }
            .padding()

            // Display fetched images
            ScrollView(.horizontal, showsIndicators: false) {
                ScrollViewReader { proxy in
                    LazyHGrid(rows: [GridItem(.fixed(imageWidth))]) {
                        switch selectedArea {
                        case .front:
                            ForEach(frontImageService.images.indices, id: \.self) { index in
                                Image(uiImage: frontImageService.images[index])
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: imageWidth, height: imageWidth)
                                    .id(index)
                            }
                        case .back:
                            ForEach(backImageService.images.indices, id: \.self) { index in
                                Image(uiImage: backImageService.images[index])
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: imageWidth, height: imageWidth)
                                    .id(index)
                            }
                        case .top:
                            ForEach(topImageService.images.indices, id: \.self) { index in
                                Image(uiImage: topImageService.images[index])
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: imageWidth, height: imageWidth)
                                    .id(index)
                            }
                        case .right:
                            ForEach(rightImageService.images.indices, id: \.self) { index in
                                Image(uiImage: rightImageService.images[index])
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: imageWidth, height: imageWidth)
                                    .id(index)
                            }
                        case .left:
                            ForEach(leftImageService.images.indices, id: \.self) { index in
                                Image(uiImage: leftImageService.images[index])
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: imageWidth, height: imageWidth)
                                    .id(index)
                            }
                        }
                    }
                    .onAppear {
                        scrollProxy = proxy
                    }
                }
            }

            // Slider to control photo index
            Slider(value: $currentIndex, in: 0...Double(max(0, getImagesForSelectedArea().count - 1))) { _ in
                scrollToImage(index: Int(currentIndex))
            }
            .onChange(of: currentIndex) { _ in
                scrollToImage(index: Int(currentIndex))
            }

            // Label to display current photo index
            Text("Image \(Int(currentIndex) + 1) of \(getImagesForSelectedArea().count)")

            // Button to trigger image selection modal
            PhotosPicker(
                selection: $vm.selectedPhotos,
                maxSelectionCount: maxPhotosToSelect,
                selectionBehavior: .ordered,
                matching: .images
            ) {
                Label("Select up to ^[\(maxPhotosToSelect) photo](inflect: true)", systemImage: "photo")
            }
            .onChange(of: vm.selectedPhotos) { newPhotos in
                vm.convertDataToImage()
                if !newPhotos.isEmpty {
                    showImageSelectionModal = true
                }
            }
        }
  
        .padding()
        .sheet(isPresented: $showImageSelectionModal) {
            ImageSelectionModalView(vm: vm, selectedArea: $selectedArea, isPresented: $showImageSelectionModal)
        }
    }

    private func loadImagesForCurrentArea() {
        let cachedImages = vm.loadCachedImages(for: selectedArea)
        if !cachedImages.isEmpty {
            updateImagesForCurrentArea(cachedImages)
        } else {
            fetchImagesFromFirebase()
        }
    }

    private func updateImagesForCurrentArea(_ images: [UIImage]) {
        switch selectedArea {
        case .front:
            frontImageService.images = images
        case .back:
            backImageService.images = images
        case .top:
            topImageService.images = images
        case .right:
            rightImageService.images = images
        case .left:
            leftImageService.images = images
        }
    }

    private func fetchImagesFromFirebase() {
        switch selectedArea {
        case .front:
            frontImageService.fetchImagesFromFirebase()
        case .back:
            backImageService.fetchImagesFromFirebase()
        case .top:
            topImageService.fetchImagesFromFirebase()
        case .right:
            rightImageService.fetchImagesFromFirebase()
        case .left:
            leftImageService.fetchImagesFromFirebase()
        }
    }

    // Helper function to get the correct image array based on the selected area
    private func getImagesForSelectedArea() -> [UIImage] {
        switch selectedArea {
        case .front:
            return frontImageService.images
        case .back:
            return backImageService.images
        case .top:
            return topImageService.images
        case .right:
            return rightImageService.images
        case .left:
            return leftImageService.images
        }
    }

    // Function to scroll to a specific image index
    private func scrollToImage(index: Int) {
        if let proxy = scrollProxy {
            proxy.scrollTo(index, anchor: .center)
        }
    }
}
struct ImageSelectionModalView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger

    @ObservedObject var vm: PhotoSelectorViewModel
    @Binding var selectedArea: Area
    @Binding var isPresented: Bool // Receive isPresented binding

    var body: some View {
        NavigationView {
            VStack {
                ScrollView(.horizontal) {
                    LazyHGrid(rows: [GridItem(.fixed(300))]) {
                        ForEach(vm.images, id: \.self) { image in
                            Image(uiImage: image)
                                .resizable()
                                .scaledToFit()
                                .frame(width: 300, height: 300)
                        }
                    }
                }
                Button("Upload Images") {
                                  vm.uploadImages(area: selectedArea)
                  AnalyticsInfoLogger.shared.logEvent("Upload_Images_Clicked", properties: ["area": selectedArea.rawValue, "count": vm.images.count])

                                  isPresented = false // Dismiss modal after upload
                              }
                              .disabled(vm.images.isEmpty)
                          }
                          .padding()
                          .navigationTitle("Selected Images")
        }
        .onAppear {
                  AnalyticsInfoLogger.shared.logEvent("Image_Selection_Modal_Opened", properties: ["area": selectedArea.rawValue])
              }
    }
}

struct CustomPicker<T: Identifiable & Equatable, C: RandomAccessCollection<T>, Content: View>: View  {
    let items: C
    @Binding var selection: T
    @ViewBuilder let itemBuilder: (T) -> Content
    var body: some View {
        HStack(spacing: 0) {
            ForEach(items) { source in
                Button {
                    selection = source
                } label: {
                    itemBuilder(source)
                        .padding()
                        .background(selection == source ? Color.gray.opacity(0.2) : Color.clear)
                        .cornerRadius(7)
                        .foregroundColor(Color.black)
                }
                .buttonStyle(PlainButtonStyle())
                .animation(.default, value: selection)
            }
        }
        .padding(4)
        .background(Color.white)
        .cornerRadius(7)
        
    }
}

struct Item: Identifiable, Equatable {
    let id: String
    let name: String
}


#Preview {
    HairProgressView()
}
