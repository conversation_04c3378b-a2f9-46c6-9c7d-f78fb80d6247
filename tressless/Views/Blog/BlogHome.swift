//
//  BlogHome.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI
import MarkdownUI

struct BlogHome: View {
    @EnvironmentObject private var blogService: BlogService
    @State private var selectedBlogPost: BlogPost?
    @Environment(\.dismiss) private var dismiss
    @State private var showBlogPostDetail = false
    
    var body: some View {
        // Remove NavigationView
        VStack {
            // Add custom title
            Text("Research")
                .font(.largeTitle)
                .fontWeight(.bold)
                .padding()
            
            Group {
                if blogService.isLoading {
                    ProgressView {
                        Text("Loading researchs...")
                    }
                } else if blogService.blogPosts.isEmpty {
                    Text("No researchs available")
                } else {
                    List(blogService.blogPosts) { post in
                        BlogPostRow(post: post)
                            .onTapGesture {
                                self.selectedBlogPost = post
                                self.showBlogPostDetail = true
                            }
                    }
                }
            }
        }
        .onAppear {
            if blogService.blogPosts.isEmpty {
                blogService.fetchBlogPosts()
            }
        }
        .sheet(item: $selectedBlogPost) { post in
            BlogPostDetail(post: post)
        }
    }
}

struct BlogPostRow: View {
    let post: BlogPost
    
    var body: some View {
        HStack {
            if let imageUrl = post.coverImageUrl {
                AsyncImage(url: URL(string: imageUrl)) { image in
                    image.resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 100, height: 100)
                        .clipped()
                } placeholder: {
                    ProgressView()
                }
                .frame(width: 100, height: 100)
                .cornerRadius(8)
            } else {
                Text("No image")
                    .frame(width: 100, height: 100)
                    .background(Color.gray.opacity(0.3))
                    .cornerRadius(8)
            }
            
            VStack(alignment: .leading) {
                Text(post.title)
                    .font(.headline)
                Text(post.brief)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
        }
    }
}

struct BlogPostDetail: View {
    let post: BlogPost
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    if let imageUrl = post.coverImageUrl {
                        AsyncImage(url: URL(string: imageUrl)) { image in
                            image.resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(height: 200)
                        } placeholder: {
                            ProgressView()
                        }
                        .frame(maxWidth: .infinity)
                        .cornerRadius(8)
                    }
                    
                    Text(post.title)
                        .font(.title)
                    Text(post.brief)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Markdown(post.content)
                        .markdownTheme(.gitHub)
                }
                .padding()
                .padding(.bottom, 66)
            }
            .navigationTitle("Research")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}


