//
//  ResultsView.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI
import StoreKit

struct ResultsView: View {
  @EnvironmentObject var logger: AnalyticsInfoLogger
    let result: HairAnalysisResult
    @Environment(\.dismiss) var dismiss
    @Environment(\.presentationMode) var presentationMode
    
    // Add this state variable
    @State private var showReviewRequest = false

    var body: some View {
        ZStack {
          VStack(alignment: .leading) {
                // Title and subtitle
                Text("Hair Analysis Results")
                    .font(.title3)
                    .fontWeight(.semibold)
                Text("Your hair analysis results provide a comprehensive overview of your hair health. ")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                Spacer()
                // Key Metrics
                VStack(alignment: .leading) {
                    Text("Key Metrics")
                        .font(.headline)
                  MetricView(value: "\(result.hairDensity)%", title:"Hair Density")
                 


                    HStack {
                        
                      MetricView(label: result.hairLossLevel, icon: "exclamationmark.triangle.fill", title: "Hair Loss Level")
                        
                      MetricView(label: "Level \(result.norwoodLevel)", icon: "chart.bar.fill", title: "Norwood Level")
                    }
                }
            
            Spacer()
                // Scalp Hair Counts
                VStack {
                    Text("Scalp Hair Counts")
                        .font(.headline)
                  Text("±10%")
                      .font(.footnote)
                      .fontWeight(.ultraLight)
                    HStack {
                        CountView(label: "Frontal", count: result.frontalHairCount)
                        Spacer()
                        CountView(label: "Occipital", count: result.occipitalHairCount)
                        Spacer()
                        CountView(label: "Vertex", count: result.vertexHairCount)
                    }
                }
            Spacer()
                // Detailed Analysis
                VStack {
                    Text("Detailed Analysis")
                        .font(.headline)
                    Text("The Hair Loss Level indicates a \(result.hairLossLevel.lowercased()) level of hair loss, which corresponds to Level \(result.norwoodLevel) on the Norwood scale. The Final Hair Density is measured at \(result.hairDensity, specifier: "%.1f")%, which is a crucial indicator of overall hair health.")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
               /*
                // Download Report Button
                Button(action: {
                  AnalyticsInfoLogger.shared.logEvent("Download_Report_Tapped")

                    // Add your action here
                }) {
                    Text("Download Report")
                        .font(.headline)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                */
                
                 
 // Pushes content to the top
            }
            .padding() // Add padding to the VStack if needed
            .scrollContentBackground(.hidden)
            .navigationBarTitle("Hair Analyze", displayMode: .inline)
        }
        .onAppear {
            logResultsViewed()
            
            // Schedule the review request
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                self.showReviewRequest = true
            }
        }
        .onChange(of: showReviewRequest) { newValue in
            if newValue {
                requestReview()
            }
        }
    }
    
    private func logResultsViewed() {
        let properties: [String: Any] = [
            "hair_density": result.hairDensity,
            "hair_loss_level": result.hairLossLevel,
            "norwood_level": result.norwoodLevel,
            "frontal_hair_count": result.frontalHairCount,
            "occipital_hair_count": result.occipitalHairCount,
            "vertex_hair_count": result.vertexHairCount
        ]
        AnalyticsInfoLogger.shared.logEvent("Results_Viewed", properties: properties)
    }

    // Add this new function
    private func requestReview() {
        guard let scene = UIApplication.shared.connectedScenes.first(where: { $0.activationState == .foregroundActive }) as? UIWindowScene else { return }
        SKStoreReviewController.requestReview(in: scene)
    }
}

struct MetricView: View {
    var label: String?
    var value: String?
    var icon: String?
    var title: String
    var body: some View {
      
      VStack {
        Text(title)
            .font(.footnote)
            .fontWeight(.ultraLight)
        HStack {
              if let icon = icon {
                  Image(systemName: icon)
                  .font(.footnote)
              }
          Text(label ?? "")
                  .font(.subheadline)
                  .fontWeight(.medium)
              if let value = value {
                  Text(value)
                      .font(.title2)
                      .fontWeight(.bold)
              }
          }
          
      }
      .foregroundColor(Color.black)
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(red: 0.965, green: 0.973, blue: 0.976))
      .cornerRadius(10)

    }
}

struct CountView: View {
    var label: String
    var count: Double
    
    var body: some View {
        VStack {
          
          VStack {
            Text("\(count, specifier: "%.0f")")
                  .font(.title2)
                .fontWeight(.bold)
            Text(" per cm²")

              .font(.footnote)
              .fontWeight(.thin)

          }
        
            Text(label)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.gray)
        }    .font(.headline)
        .frame(maxWidth: .infinity)
        .padding()
        .foregroundColor(Color.black)
        .background(Color(red: 0.965, green: 0.973, blue: 0.976))
        .cornerRadius(10)
    }
}

#Preview {
    ResultsView(result: HairAnalysisResult(
        hairDensity: 83.7,
        hairLossLevel: "Moderate",
        norwoodLevel: "3",
        frontalHairCount: 82,
        occipitalHairCount: 150,
        vertexHairCount: 13
    ))
}
