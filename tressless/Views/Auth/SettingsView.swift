//
//  SettingsView.swift
//  tressless
//
//  Created by <PERSON> on 13.08.2024.
//

import SwiftUI
import Firebase
import SwiftfulFirebaseAuth
import RevenueCat
import RevenueCatUI

struct SettingsView: View {

    @Environment(\.auth) private var authManager
    @Environment(RootState.self) private var root

    @State private var userIsAnonymous: Bool = false
    @State private var errorAlert: AnyAppAlert? = nil

    var body: some View {
      NavigationView {
             VStack {
                 NavigationLink(destination: PayView(
                     onDismiss: {},
                     onPurchaseComplete: {},
                     placementId: "onboarding",
                     currentStep: .constant(0)
                 )) {
                     Text("Go to Paywall")
                         .padding()
                         .background(Color.blue)
                         .foregroundColor(.white)
                         .cornerRadius(10)
                 }
             }
             .navigationTitle("Home")
         }
     
      
      /*
        NavigationStack {
            List {
                Button("Log out") {
                    onSignOutPressed()
                }
                
                if userIsAnonymous {
                    VStack {
                        SignInWithAppleButtonView()
                            .frame(height: 55)
                            .asButton {
                                Task { @MainActor in
                                    do {
                                        let (authUser, isNewUser) = try await authManager.signInApple()
                                        userIsAnonymous = authUser.isAnonymous
                                    } catch {
                                        print(error)
                                        errorAlert = AnyAppAlert(error: error)
                                    }
                                }
                            }
                        
                        Text("Click here to test linking anonymous to Apple SSO.")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                }
            }
            .navigationTitle("Settings")
            .onAppear {
                userIsAnonymous = authManager.currentUser.profile?.isAnonymous == true
            }
        }
      */
    }

    @MainActor
    private func onSignOutPressed() {
        do {
            try authManager.signOut()

            // 🎯 SMART SIGN OUT: Let RootView handle routing based on subscription status
            root.updateViewState(showTabbar: false)

            AnalyticsInfoLogger.shared.logEvent("User_Signed_Out_From_Settings")
        } catch {
            print(error)
        }
    }
}

#Preview {
    SettingsView()
        .environment(RootState())
}
