//
//  CreateAccountView.swift
//  tressless
//
//  Created by <PERSON> on 14.09.2024.
//

import SwiftUI
import SwiftfulF<PERSON>baseAuth
import Firebase
import RevenueCat
import FirebaseFirestore
import FirebaseAuth

// Custom error for account creation failures
enum AccountCreationError: LocalizedError {
    case noPremiumSubscription
    case revenueCatLinkingFailed

    var errorDescription: String? {
        switch self {
        case .noPremiumSubscription:
            return "A premium subscription is required to create an account. Please complete your purchase first."
        case .revenueCatLinkingFailed:
            return "Failed to link your purchase to your account. Please try again or contact support."
        }
    }
}

struct CreateAccountView: View {
    @Environment(\.dismiss) var dismiss
    @EnvironmentObject var rootState: RootState
    @EnvironmentObject var logger: AnalyticsInfoLogger
    var onLoginSuccess: (() -> Void)?

    // Add callback to navigate back to paywall if account creation fails
    var onAccountCreationFailed: (() -> Void)?

    @Environment(\.auth) private var authManager

    @State private var errorAlert: AnyAppAlert? = nil
    @State private var showPhoneView: Bool = false

    var body: some View {
        ZStack {
            Color("Background").ignoresSafeArea()

            VStack(spacing: 20) {
                Image("analysis")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(height: 200)
                    .padding()


                Text("Regrow Your Confidence: The Ultimate Hair Restoration App")
                    .font(.title)
                    .fontWeight(.light)
                Text("Get started by signing in with Apple or Google ").font(.subheadline).fontWeight(.light)

                // 🎯 Subscription required error removed - account creation never blocks now

                Spacer()

                VStack(spacing: 16) {
                    SignInWithAppleButtonView()
                        .frame(height: 55)
                        .asButton {
                            signInWithAppleButtonPressed()
                        }

                    SignInWithGoogleButtonView()
                        .frame(height: 55)
                        .asButton {
                            signInWithGoogleButtonPressed()
                        }
                }
                .padding()

                HStack(spacing: 8) {
                    Link(destination: URL(string: "https://follicleai.com/terms-and-conditions")!, label: {
                        Text("Terms of Service")
                            .underline()
                    })
                    Circle()
                        .fill(Color("Background 4"))
                        .frame(width: 4, height: 4)
                    Link(destination: URL(string: "https://follicleai.com/privacy-policy")!, label: {
                        Text("Privacy Policy")
                            .underline()
                    })
                }
                .font(.footnote)
                .foregroundColor(Color("Background 4"))
            }
            .padding()
        }
        .showCustomAlert(alert: $errorAlert)
        .onAppear {
            AnalyticsInfoLogger.shared.logEvent("Create_Account_View_Shown")
        }
    }
    
    private func handleSuccessfulLogin() {
        rootState.updateViewState(showTabbar: true)

        if let callback = onLoginSuccess {
            callback()
        }

        dismiss()
    }

    @MainActor
    func signInWithAppleButtonPressed() {
        Task {
            do {
                let (authUser, isNewUser) = try await authManager.signInApple()

                if isNewUser {
                    // Create user document
                    try await createUserDocument(userId: authUser.uid)
                }

                // CRITICAL: This will throw if no premium subscription or linking fails
                try await createOrUpdateSubscriptionEntry(userId: authUser.uid)

                AnalyticsInfoLogger.shared.identifyUser(userId: authUser.uid, traits: nil)

                handleSuccessfulLogin()
            } catch {
                // 🎯 SIMPLIFIED: All errors are treated the same now
                errorAlert = AnyAppAlert(error: error)
                AnalyticsInfoLogger.shared.logEvent("Account_Creation_Failed", properties: [
                    "error": error.localizedDescription,
                    "sign_in_method": "apple"
                ])
            }
        }
    }

    @MainActor
    func signInWithGoogleButtonPressed() {
        Task {
            do {
                guard let clientId = FirebaseApp.app()?.options.clientID else {
                    throw URLError(.cannotCreateFile)
                }

                let (authUser, isNewUser) = try await authManager.signInGoogle(GIDClientID: clientId)

                if isNewUser {
                    // Create user document
                    try await createUserDocument(userId: authUser.uid)
                }

                // CRITICAL: This will throw if no premium subscription or linking fails
                try await createOrUpdateSubscriptionEntry(userId: authUser.uid)

                AnalyticsInfoLogger.shared.identifyUser(userId: authUser.uid, traits: nil)
                handleSuccessfulLogin()
            } catch {
                // 🎯 SIMPLIFIED: All errors are treated the same now
                errorAlert = AnyAppAlert(error: error)
                AnalyticsInfoLogger.shared.logEvent("Account_Creation_Failed", properties: [
                    "error": error.localizedDescription,
                    "sign_in_method": "google"
                ])
            }
        }
    }

    @MainActor
    func signInWithPhoneButtonPressed() {
        showPhoneView = true
    }

    @MainActor
    func createOrUpdateSubscriptionEntry(userId: String) async throws {
        let db = Firestore.firestore()

        // CRITICAL: Link any anonymous purchases to this user account
        // If linking fails, we MUST throw an error to prevent account creation

        // First, check what the anonymous user has BEFORE login
        let anonymousCustomerInfo = try await Purchases.shared.customerInfo()

        // Add a small delay to ensure purchase is fully processed
        try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds

        let logInResult = try await Purchases.shared.logIn(userId)

        // Check RevenueCat customer info (now includes any linked anonymous purchases)
        let customerInfo = try await Purchases.shared.customerInfo()
        let isPremium = customerInfo.entitlements["premium"]?.isActive ?? false

        // 🎯 SIMPLE: Create account regardless of subscription status (like main2)
        let subscriptionData: [String: Any] = [
            "subscriptionType": isPremium ? "premium" : "free",
            "startDate": Timestamp(date: Date()),
            "endDate": NSNull(),
            "analyzesLeft": isPremium ? 1000 : 0,
            "analysesUsed": 0,
            "lastUpdated": Timestamp(date: Date())
        ]

        // Specify the document reference
        let userDocRef = db.collection("users").document(userId)
        let subscriptionDocRef = userDocRef.collection("subscriptions").document("current_subscription")

        // First, ensure the user document exists
        try await userDocRef.setData([:], merge: true)

        // Then, set the subscription data
        try await subscriptionDocRef.setData(subscriptionData, merge: true)

        AnalyticsInfoLogger.shared.logEvent("Account_Created", properties: [
            "type": isPremium ? "premium" : "free",
            "user_id": userId,
            "had_anonymous_purchase": isPremium
        ])
    }

    @MainActor
    func createUserDocument(userId: String) async throws {
        let db = Firestore.firestore()
        let userRef = db.collection("users").document(userId)

        let userData: [String: Any] = [
            "createdAt": Timestamp(date: Date()),
            "lastLogin": Timestamp(date: Date()),
            "lastActivityAt": Timestamp(date: Date()) // Required for optimized inactivity tracking
        ]

        try await userRef.setData(userData, merge: true)
    }
}

// Add this preview struct at the end of the file
struct CreateAccountView_Previews: PreviewProvider {
    static var previews: some View {
        CreateAccountView(onAccountCreationFailed: {
            // Handle account creation failure
        })
            .environmentObject(RootState())
    }
}



