//
//  RootView.swift
//  tressless
//
//  Created by <PERSON> on 14.09.2024.
//

import SwiftUI
import RevenueCat
import Firebase

class RootState: ObservableObject {
    @Published var showTabbarView: Bool

    init() {
        let userDefaultsValue = UserDefaults.showTabbarView
        self.showTabbarView = userDefaultsValue
    }
    
    func updateViewState(showTabbar: Bool) {
        showTabbarView = showTabbar
        UserDefaults.showTabbarView = showTabbar
    }
}

struct RootView: View {
    @StateObject private var rootState = RootState()
    @State private var showOnboarding: Bool

    init() {
        // Check if it's the first launch
        let isFirstLaunch = !UserDefaults.standard.bool(forKey: "hasLaunchedBefore")
        _showOnboarding = State(initialValue: isFirstLaunch)

        // Set the flag to indicate the app has been launched
        if isFirstLaunch {
            UserDefaults.standard.set(true, forKey: "hasLaunchedBefore")
        }
    }

    var body: some View {
        if showOnboarding {
            OnboardView(show: $showOnboarding)
                .environmentObject(rootState)
        } else {
                RootViewBuilder(
                    showTabbar: rootState.showTabbarView,
                    tabbarView: {
                        TabbarView()
                    },
                    onboardingView: {
                        // 🎯 MAIN2 APPROACH: Always show OnboardView (welcome screen first)
                        OnboardView(show: .constant(true))
                    }
                )
                .environmentObject(rootState)
        }
    }
}

struct RootViewBuilder<TabbarView: View, OnboardingView: View>: View {
    
    let showTabbar: Bool
    @ViewBuilder var tabbarView: TabbarView
    @ViewBuilder var onboardingView: OnboardingView
        
    var body: some View {
        ZStack {
            if showTabbar {
                tabbarView
                    .transition(.move(edge: .trailing))
            } else {
                onboardingView
                    .transition(.move(edge: .leading))
            }
        }
        .animation(.smooth, value: showTabbar)
    }
}

#Preview {
    RootView()
}

fileprivate extension UserDefaults {
    
    private enum Keys {
        static let showTabbarView = "showTabbarView"
    }
    
    static var showTabbarView: Bool {
        get {
            return standard.bool(forKey: Keys.showTabbarView)
        }
        set {
            standard.set(newValue, forKey: Keys.showTabbarView)
        }
    }
}
