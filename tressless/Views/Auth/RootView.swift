//
//  RootView.swift
//  tressless
//
//  Created by <PERSON> on 14.09.2024.
//

import SwiftUI
import RevenueCat
import Firebase

class RootState: ObservableObject {
    @Published var showTabbarView: Bool

    init() {
        let userDefaultsValue = UserDefaults.showTabbarView
        self.showTabbarView = userDefaultsValue
    }
    
    func updateViewState(showTabbar: Bool) {
        showTabbarView = showTabbar
        UserDefaults.showTabbarView = showTabbar
    }
}

struct RootView: View {
    @StateObject private var rootState = RootState()
    @State private var showOnboarding: Bool
    @State private var isCheckingSubscription: Bool = true

    init() {
        // Check if it's the first launch
        let isFirstLaunch = !UserDefaults.standard.bool(forKey: "hasLaunchedBefore")
        _showOnboarding = State(initialValue: isFirstLaunch)

        // Set the flag to indicate the app has been launched
        if isFirstLaunch {
            UserDefaults.standard.set(true, forKey: "hasLaunchedBefore")
        }
    }

    var body: some View {
        ZStack {
            if isCheckingSubscription {
                // Show loading while checking subscription status
                VStack {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("Loading...")
                        .padding(.top)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color("Background"))
            } else if showOnboarding {
                OnboardView(
                    show: $showOnboarding
                )
                .environmentObject(rootState)
            } else {
                RootViewBuilder(
                    showTabbar: rootState.showTabbarView,
                    tabbarView: {
                        TabbarView()
                    },
                    onboardingView: {
                        // 🎯 GENUINE FIX: Simple sign-in screen for returning users (not full onboarding)
                        CreateAccountView(onAccountCreationFailed: nil)
                    }
                )
                .environmentObject(rootState)
            }
        }
        .onAppear {
            checkSubscriptionStatusAndRoute()
        }
        .onChange(of: rootState.showTabbarView) { newValue in
            // When user signs out (showTabbarView becomes false), re-check subscription
            if !newValue && Auth.auth().currentUser == nil {
                checkSubscriptionStatusAndRoute()
            }
        }
    }

    // 🎯 SMART DETECTION: Check RevenueCat for existing purchases
    private func checkSubscriptionStatusAndRoute() {
        // Only check on first app launch or when not authenticated
        guard Auth.auth().currentUser == nil else {
            // User is already authenticated, use normal flow
            isCheckingSubscription = false
            return
        }

        Task {
            do {
                // Check RevenueCat for any existing purchases (anonymous or otherwise)
                let customerInfo = try await Purchases.shared.customerInfo()
                let hasPremiumSubscription = customerInfo.entitlements["premium"]?.isActive ?? false

                await MainActor.run {
                    isCheckingSubscription = false

                    if hasPremiumSubscription {
                        // 🎯 Premium user - still show full onboarding (no skip)
                        showOnboarding = true
                        AnalyticsInfoLogger.shared.logEvent("Returning_Premium_User_Detected")
                    } else {
                        // 🎯 New user or no subscription - show full onboarding from start
                        // showOnboarding is already set in init()
                        AnalyticsInfoLogger.shared.logEvent("New_User_Onboarding_Started")
                    }
                }
            } catch {
                // If RevenueCat check fails, default to onboarding (safe fallback)
                await MainActor.run {
                    isCheckingSubscription = false
                    // showOnboarding is already set in init()

                    AnalyticsInfoLogger.shared.logEvent("Subscription_Check_Failed", properties: [
                        "error": error.localizedDescription
                    ])
                }
            }
        }
    }
}

struct RootViewBuilder<TabbarView: View, OnboardingView: View>: View {
    
    let showTabbar: Bool
    @ViewBuilder var tabbarView: TabbarView
    @ViewBuilder var onboardingView: OnboardingView
        
    var body: some View {
        ZStack {
            if showTabbar {
                tabbarView
                    .transition(.move(edge: .trailing))
            } else {
                onboardingView
                    .transition(.move(edge: .leading))
            }
        }
        .animation(.smooth, value: showTabbar)
    }
}

#Preview {
    RootView()
}

fileprivate extension UserDefaults {
    
    private enum Keys {
        static let showTabbarView = "showTabbarView"
    }
    
    static var showTabbarView: Bool {
        get {
            return standard.bool(forKey: Keys.showTabbarView)
        }
        set {
            standard.set(newValue, forKey: Keys.showTabbarView)
        }
    }
}
