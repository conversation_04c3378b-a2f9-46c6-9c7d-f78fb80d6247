//
//  ReminderView.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI
import FirebaseAuth
import FirebaseFirestore

struct MedicationCompletion: Codable, Identifiable, Equatable {
    let id: String
    let medicationId: String
    let date: Date
    var isCompleted: Bool
}

class MedicationManager: ObservableObject {
    @Published var medications: [MedicationDocument] = []
    @Published var completions: [MedicationCompletion] = []

    private let db = Firestore.firestore()

    func loadMedications() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        let db = Firestore.firestore()

        db.collection("users").document(userId).collection("medications")
            .getDocuments { (querySnapshot, error) in
                if let error = error {
                    print("Error getting medications: \(error)")
                } else {
                    self.medications = querySnapshot?.documents.compactMap { document -> MedicationDocument? in
                        let data = document.data()
                        guard let name = data["name"] as? String,
                              let time = data["time"] as? Timestamp,
                              let repeatInterval = data["repeatInterval"] as? String,
                              let dosage = data["dosage"] as? String,
                              let dates = data["dates"] as? [Timestamp] else {
                            return nil
                        }
                        return MedicationDocument(id: document.documentID, name: name, time: time.dateValue(), repeatInterval: MedicationItem.RepeatInterval(rawValue: repeatInterval) ?? .daily, dosage: dosage, dates: dates.map { $0.dateValue() })
                    } ?? []
                }
            }
    }

    func loadCompletionsForDateRange(from startDate: Date, to endDate: Date, completion: @escaping () -> Void) {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        db.collection("users").document(userId).collection("medication_completions")
            .whereField("date", isGreaterThanOrEqualTo: startDate)
            .whereField("date", isLessThan: endDate)
            .getDocuments { [weak self] (querySnapshot, error) in
                if let error = error {
                    print("Error getting completions: \(error)")
                } else {
                    self?.completions = querySnapshot?.documents.compactMap { document -> MedicationCompletion? in
                        let data = document.data()
                        guard let medicationId = data["medicationId"] as? String,
                              let timestamp = data["date"] as? Timestamp,
                              let isCompleted = data["isCompleted"] as? Bool else {
                            return nil
                        }
                        return MedicationCompletion(id: document.documentID, medicationId: medicationId, date: timestamp.dateValue(), isCompleted: isCompleted)
                    } ?? []
                    completion()
                }
            }
    }

    func toggleCompletion(for medication: MedicationDocument, on date: Date) {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        
        if let existingCompletion = completions.first(where: { $0.medicationId == medication.id && calendar.isDate($0.date, inSameDayAs: startOfDay) }) {
            // Update existing completion
            let updatedCompletion = MedicationCompletion(id: existingCompletion.id, medicationId: existingCompletion.medicationId, date: existingCompletion.date, isCompleted: !existingCompletion.isCompleted)
            
            if let index = completions.firstIndex(where: { $0.id == existingCompletion.id }) {
                completions[index] = updatedCompletion
            }
            
            db.collection("users").document(userId).collection("medication_completions")
                .document(existingCompletion.id)
                .updateData(["isCompleted": updatedCompletion.isCompleted])
        } else {
            // Create new completion
            let newCompletion = MedicationCompletion(id: UUID().uuidString, medicationId: medication.id, date: startOfDay, isCompleted: true)
            completions.append(newCompletion)
            
            db.collection("users").document(userId).collection("medication_completions")
                .document(newCompletion.id)
                .setData([
                    "medicationId": newCompletion.medicationId,
                    "date": Timestamp(date: newCompletion.date),
                    "isCompleted": newCompletion.isCompleted
                ])
        }
    }

    func deletemedication(_ medication: MedicationDocument) {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        let db = Firestore.firestore()
        
        db.collection("users").document(userId).collection("medications").document(medication.id).delete { error in
            if let error = error {
                print("Error deleting medication: \(error)")
            } else {
                self.medications.removeAll { $0.id == medication.id }
            }
        }
    }

    func updateMedications(_ medications: [MedicationDocument]) {
        self.medications = medications
    }

    func loadAllCompletions() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        db.collection("users").document(userId).collection("medication_completions")
            .getDocuments { [weak self] (querySnapshot, error) in
                if let error = error {
                    print("Error getting completions: \(error)")
                } else {
                    self?.completions = querySnapshot?.documents.compactMap { document -> MedicationCompletion? in
                        let data = document.data()
                        guard let medicationId = data["medicationId"] as? String,
                              let timestamp = data["date"] as? Timestamp,
                              let isCompleted = data["isCompleted"] as? Bool else {
                            return nil
                        }
                        return MedicationCompletion(id: document.documentID, medicationId: medicationId, date: timestamp.dateValue(), isCompleted: isCompleted)
                    } ?? []
                    self?.objectWillChange.send()
                }
            }
    }
}

struct MedicationView: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger
    @State var selectedDate = Date()
    @State var isAddingMedication = false
    @StateObject private var medicationManager = MedicationManager()

    var body: some View {
        VStack {
            // Header
            VStack {
                HStack(alignment: .center) {
                    Text("Hello!")
                        .font(.custom("KdamThmorPro-Regular", size: 20))
                        .fontWeight(.bold)
                }
                .padding(.bottom)
                .frame(maxWidth: .infinity, alignment: .center)

                // Calendar Strip
                CalendarStrip(selectedDate: $selectedDate, onDateSelected: { date in
                    medicationManager.loadCompletionsForDateRange(from: date, to: Calendar.current.date(byAdding: .day, value: 1, to: date)!) {
                        // No need to do anything here, as the completions are automatically updated
                    }
                })
                .id("CalendarStrip")
            }
            .padding()
            .background(Color(UIColor(hexString: "#DDE6CC")))

            // Medication List
            HStack {
                Text("Today's medication")
                    .font(.custom("KdamThmorPro-Regular", size: 20))
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity, alignment: .leading)
                Spacer()
                Button(action: {
                    isAddingMedication = true
                }) {
                    Image(systemName: "plus")
                        .font(.caption)
                        .foregroundColor(Color("Background"))
                        .padding()
                        .padding(.horizontal, 15)
                        .background(Color("Background 4"))
                        .cornerRadius(10)
                }
                .sheet(isPresented: $isAddingMedication) {
                    AddMedicationView2(medications: $medicationManager.medications, medicationManager: medicationManager)
                }
            }
            .padding(5)
            .padding(.horizontal, 15)

            List {
                ForEach(medicationManager.medications.filter { medication in
                    medication.dates.contains(where: { Calendar.current.isDate($0, inSameDayAs: selectedDate) })
                }) { medication in
                  MedicationRow(medicationManager: medicationManager, medication: medication, selectedDate: selectedDate)
                        .listRowBackground(Color("Background"))
                }
                .onDelete(perform: deleteMedication)
            }
            .padding(.bottom, 40)
            .listStyle(.plain)
        }
        .background(Color("Background"))
        .onAppear {
            medicationManager.loadMedications()
            medicationManager.loadAllCompletions()
            AnalyticsInfoLogger.shared.logEvent("Medication_View_Opened")
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ScrollToDate"))) { notification in
            if let date = notification.object as? Date {
                selectedDate = date
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation {
                        // Scroll to the selected date in the CalendarStrip
                        NotificationCenter.default.post(name: Notification.Name("ScrollCalendarStrip"), object: date)
                    }
                }
            }
        }
    }

    private func deleteMedication(at offsets: IndexSet) {
        offsets.forEach { index in
            let medication = medicationManager.medications.filter { medication in
                medication.dates.contains(where: { Calendar.current.isDate($0, inSameDayAs: selectedDate) })
            }[index]
            medicationManager.deletemedication(medication)
            AnalyticsInfoLogger.shared.logEvent("Medication_Deleted", properties: ["medication_name": medication.name])
        }
    }
}

struct MedicationRow: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger
    @ObservedObject var medicationManager: MedicationManager
    var medication: MedicationDocument
    var selectedDate: Date
    @State private var isEditing = false

    var body: some View {
        HStack {
            Image(systemName: "capsule.fill")
                .foregroundColor(Color("Background 2"))
                .padding(6)
                .clipShape(Circle())
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 8) {
                    Text(medication.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                        .strikethrough(isCompleted, color: .black) // Add this line

                    Spacer()
                    Text(medication.time, style: .time)
                        .font(.subheadline)
                }
                Text("\(medication.dosage) \(medication.repeatInterval.rawValue.capitalized)")
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }.padding(6)
            Spacer()
            
            // Checkbox for completion status
            Image(systemName: isCompleted ? "checkmark.circle.fill" : "circle")
                .foregroundColor(isCompleted ? .green : .gray)
                .onTapGesture {
                    medicationManager.toggleCompletion(for: medication, on: selectedDate)
                    AnalyticsInfoLogger.shared.logEvent("Medication_Completion_Toggled", properties: [
                        "medication_name": medication.name,
                        "is_completed": !isCompleted
                    ])
                }
        }
        .padding()
        .background(Color(UIColor(hexString: "#DDEAF8")))
        .cornerRadius(10)
        .onTapGesture {
            isEditing = true
            AnalyticsInfoLogger.shared.logEvent("Medication_Edit_Started", properties: ["medication_name": medication.name])
        }
        .sheet(isPresented: $isEditing) {
            EditMedicationsView(medications: $medicationManager.medications, medicationToEdit: medication, medicationManager: medicationManager)
        }
    }

    private var isCompleted: Bool {
        medicationManager.completions.contains { completion in
            completion.medicationId == medication.id &&
            Calendar.current.isDate(completion.date, inSameDayAs: selectedDate) &&
            completion.isCompleted
        }
    }
}

struct AddMedicationView2: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger

    @Environment(\.dismiss) var dismiss
    @Binding var medications: [MedicationDocument]
    @State private var startDate: Date = Date()

    @State private var name: String = ""
    @State private var dosage: String = ""

    @State private var time: Date = Date()
    @State private var repeatInterval: MedicationItem.RepeatInterval = .daily
    @ObservedObject var medicationManager: MedicationManager

    var body: some View {
        NavigationView {
            Section{
                Form {
                    TextField("Medicine Name", text: $name)
                    DatePicker("Time", selection: $time, displayedComponents: .hourAndMinute)
                    Picker("Repeat", selection: $repeatInterval) {
                        ForEach(MedicationItem.RepeatInterval.allCases, id: \.self) { interval in
                            Text(interval.rawValue.capitalized).tag(interval)
                        }
                    }
                    DatePicker("Start Date", selection: $startDate, displayedComponents: .date)
                }
            }
            .listRowBackground(Color(UIColor(hexString: "#ECDFEF")))
            
            .navigationTitle("Add Reminder")
            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        let dates = calculateMedicationDates(from: startDate, interval: repeatInterval)
                        addMedications(dates: dates, name: name, time: time, repeatInterval: repeatInterval, dosage: dosage)
                        dismiss()
                    }
                }
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .toolbarBackground(Color(UIColor.systemBackground), for: .navigationBar)
            .toolbarBackground(.visible, for: .navigationBar)
        }
        .onAppear {
            AnalyticsInfoLogger.shared.logEvent("Add_Medication_View_Opened")
        }
    }

    func addMedications(dates: [Date], name: String, time: Date, repeatInterval: MedicationItem.RepeatInterval, dosage: String) {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        let medicationsRef = db.collection("users").document(userId).collection("medications")

        let medicationDocument = MedicationDocument(id: UUID().uuidString, name: name, time: time, repeatInterval: repeatInterval, dosage: dosage, dates: dates)

        let timestampTime = Timestamp(date: time)
        let timestampDates = dates.map { Timestamp(date: $0) }

        do {
            try medicationsRef.document(medicationDocument.id).setData([
                "name": name,
                "time": timestampTime,
                "repeatInterval": repeatInterval.rawValue,
                "dosage": dosage,
                "dates": timestampDates
            ])
            AnalyticsInfoLogger.shared.logEvent("Medication_Added", properties: [
                "medication_name": name,
                "repeat_interval": repeatInterval.rawValue
            ])
            // Update the medications array in the MedicationManager
            var updatedMedications = medicationManager.medications
            updatedMedications.append(medicationDocument)
            medicationManager.updateMedications(updatedMedications)
        } catch {
            print("Error saving medication: \(error)")
            AnalyticsInfoLogger.shared.logEvent("Medication_Add_Failed", properties: ["error": error.localizedDescription])
        }
    }
}

func calculateMedicationDates(from startDate: Date, interval: MedicationItem.RepeatInterval) -> [Date] {
    var dates = [startDate]
    let calendar = Calendar.current
    var nextDate = startDate

    let endDate = calendar.date(byAdding: .day, value: 30, to: startDate)! // Calculate end date (30 days from start)

    while nextDate <= endDate { // Change condition to use <=
        switch interval {
            case .daily:
                nextDate = calendar.date(byAdding: .day, value: 1, to: nextDate)!
            case .weekly:
                nextDate = calendar.date(byAdding: .day, value: 7, to: nextDate)!
            case .monthly:
                nextDate =  calendar.date(byAdding: .day, value: 30, to: nextDate)!
            case .noReminder:
                break // Exit the loop for 'noReminder'
        }
        if nextDate <= endDate { // Add this check to prevent adding dates beyond the end date
            dates.append(nextDate)
        }
    }

    return dates
}

struct CalendarStrip: View {
    @Binding var selectedDate: Date
    var onDateSelected: (Date) -> Void
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView(.horizontal, showsIndicators: false) {
                HStack {
                    ForEach(getWeekDays(), id: \.self) { day in
                        CalendarDayView(day: day, isSelected: Calendar.current.isDate(day, inSameDayAs: selectedDate))
                            .onTapGesture {
                                selectedDate = day
                                onDateSelected(day)
                            }
                            .id(day)
                    }
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ScrollCalendarStrip"))) { notification in
                if let date = notification.object as? Date {
                    withAnimation {
                        proxy.scrollTo(date, anchor: .center)
                    }
                }
            }
        }
    }
    
    func getWeekDays() -> [Date] {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        var days = [Date]()
        for i in 0..<45 { // Generate dates for the next 45 days
            if let day = calendar.date(byAdding: .day, value: i, to: today) {
                days.append(day)
            }
        }
        return days
    }
}

struct CalendarDayView: View {
    let day: Date
    let isSelected: Bool
    
    var body: some View {
        VStack {
            Text(getDayShortName(from: day)).textCase(.uppercase)
                .font(.subheadline)
                .frame(minWidth: UIScreen.main.bounds.width/12)
            Text(getDayNumber(from: day))
                .frame(minWidth: UIScreen.main.bounds.width/12)
        }
        .padding(5)
        .foregroundColor(isSelected ? .white : Color("Background 2")) // Set foreground color based on selection
        .background(isSelected ? Color("Background 2") : Color.white) // Set background color based on selection
        .cornerRadius(10)
        
    }
    
    func getDayShortName(from date: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "EE" // Use "EEE" for full name (e.g., "Mon" or "Monday")
        return dateFormatter.string(from: date)
    }

    func getDayNumber(from date: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "d"
        return dateFormatter.string(from: date)
    }
}

struct EditMedicationsView: View {
    @EnvironmentObject var logger: AnalyticsInfoLogger

    @Environment(\.dismiss) var dismiss
    @Binding var medications: [MedicationDocument]
    @ObservedObject var medicationManager: MedicationManager
    var medicationToEdit: MedicationDocument

    @State private var startDate: Date
    @State private var name: String
    @State private var dosage: String
    @State private var time: Date
    @State private var repeatInterval: MedicationItem.RepeatInterval
    @State private var dates: [Date]

    init(medications: Binding<[MedicationDocument]>, medicationToEdit: MedicationDocument, medicationManager: MedicationManager) {
        self._medications = medications
        self.medicationToEdit = medicationToEdit
        self.medicationManager = medicationManager
        self._startDate = State(initialValue: medicationToEdit.dates.first ?? Date())
        self._name = State(initialValue: medicationToEdit.name)
        self._dosage = State(initialValue: medicationToEdit.dosage)
        self._time = State(initialValue: medicationToEdit.time)
        self._repeatInterval = State(initialValue: medicationToEdit.repeatInterval)
        self._dates = State(initialValue: medicationToEdit.dates)
        UITableView.appearance().backgroundColor = .clear
    }

    var body: some View {
        NavigationView {
            Form {
                Section{
                    TextField("Medicine Name", text: $name)
                        .font(.title2)
                        .padding(4)
                    DatePicker("Time", selection: $time, displayedComponents: .hourAndMinute)
                    Picker("Repeat", selection: $repeatInterval) {
                        ForEach(MedicationItem.RepeatInterval.allCases, id: \.self) { interval in
                            Text(interval.rawValue.capitalized).tag(interval)
                        }
                    }
                    DatePicker("Start Date", selection: $startDate, displayedComponents: .date)
                }
                .padding(4)
                .listRowBackground(Color(UIColor(hexString: "#ECDFEF")))
            }
            .navigationTitle("Edit Reminder")
            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        saveChanges()
                        dismiss()
                    }
                }
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            AnalyticsInfoLogger.shared.logEvent("Edit_Medication_View_Opened", properties: ["medication_name": medicationToEdit.name])
        }
    }

    func saveChanges() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        let medicationRef = db.collection("users").document(userId).collection("medications").document(medicationToEdit.id)

        let timestampTime = Timestamp(date: time)
        let timestampDates = dates.map { Timestamp(date: $0) }

        do {
            try medicationRef.updateData([
                "name": name,
                "time": timestampTime,
                "repeatInterval": repeatInterval.rawValue,
                "dosage": dosage,
                "dates": timestampDates
            ])
            AnalyticsInfoLogger.shared.logEvent("Medication_Updated", properties: [
                "medication_name": name,
                "repeat_interval": repeatInterval.rawValue
            ])
            // Update the medication in the local array
            if let index = medications.firstIndex(where: { $0.id == medicationToEdit.id }) {
                medications[index] = MedicationDocument(id: medicationToEdit.id, name: name, time: time, repeatInterval: repeatInterval, dosage: dosage, dates: dates)
            }
        } catch {
            print("Error updating medication: \(error)")
            AnalyticsInfoLogger.shared.logEvent("Medication_Update_Failed", properties: [
                "medication_name": name,
                "error": error.localizedDescription
            ])
        }
    }
}

#Preview {
    MedicationView()
}


