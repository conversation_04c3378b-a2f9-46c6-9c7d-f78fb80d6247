//
//  HomeView.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI
import FirebaseAuth
import FirebaseFirestore
import RevenueCat
import RevenueCatUI
import FirebaseRemoteConfig

struct WeeklyMedicationSummary: View {
    @ObservedObject var medicationManager: MedicationManager
    @Binding var selectedTab: Tab
    @State private var weekStart: Date = Date().startOfWeek
    @State private var completionStatus: [Date: CompletionStatus] = [:]
    @State private var scrollViewProxy: ScrollViewProxy?
    @State private var hasScrolledToToday = false
    @State private var selectedDate: Date?

    enum CompletionStatus {
        case allCompleted
        case partiallyCompleted
        case noneCompleted
        case noMedications
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Dedication Report")
                .font(.headline)
                .padding(.bottom, 4)

            ScrollViewReader { proxy in
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(dateRang<PERSON>, id: \.self) { date in
                            DaySummary(date: date, status: completionStatus[date] ?? .noMedications)
                                .id(date)
                                .onTapGesture {
                                    selectedDate = date
                                }
                        }
                    }
                }
                .onAppear {
                    scrollViewProxy = proxy
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        scrollToToday()
                    }
                }
            }
        }
        .padding()
        .cornerRadius(10)
        .onAppear(perform: loadAllData)
        .onChange(of: medicationManager.completions) { _ in
            loadAllData()
        }
        .sheet(item: $selectedDate) { date in
            DayMedicationsView(date: date, medicationManager: medicationManager, selectedTab: $selectedTab)
                .presentationDetents([.fraction(0.6)])
        }
    }

    private var dateRange: [Date] {
        let calendar = Calendar.current
        let today = Date()
        let endDate = today
        
        // Find the earliest date with medications in the last 30 days
        let thirtyDaysAgo = calendar.date(byAdding: .day, value: -30, to: today)!
        let startDate = max(
            medicationManager.medications.compactMap { $0.dates.first }.filter { $0 >= thirtyDaysAgo }.min() ?? today,
            thirtyDaysAgo
        )
        
        return calendar.generateDates(
            inside: DateInterval(start: startDate, end: endDate),
            matching: DateComponents(hour: 0, minute: 0, second: 0)
        )
    }

    private func loadAllData() {
        let calendar = Calendar.current
        let today = Date()
        let startDate = calendar.date(byAdding: .day, value: -30, to: today)!
        
        medicationManager.loadCompletionsForDateRange(from: startDate, to: today) {
            updateAllCompletionStatuses()
            if !hasScrolledToToday {
                scrollToToday()
            }
        }
    }

    private func scrollToToday() {
        let today = Calendar.current.startOfDay(for: Date())
        withAnimation {
            scrollViewProxy?.scrollTo(today, anchor: .center)
        }
        hasScrolledToToday = true
    }

    private func updateAllCompletionStatuses() {
        for date in dateRange {
            updateCompletionStatus(for: date)
        }
    }

    private func updateCompletionStatus(for date: Date) {
        let medicationsForDay = medicationManager.medications.filter { medication in
            medication.dates.contains { Calendar.current.isDate($0, inSameDayAs: date) }
        }

        let completionsForDay = medicationManager.completions.filter { completion in
            Calendar.current.isDate(completion.date, inSameDayAs: date)
        }

        if medicationsForDay.isEmpty {
            completionStatus[date] = .noMedications
        } else {
            let completedCount = completionsForDay.filter { $0.isCompleted }.count
            if completedCount == medicationsForDay.count && completedCount > 0 {
                completionStatus[date] = .allCompleted
            } else if completedCount > 0 {
                completionStatus[date] = .partiallyCompleted
            } else {
                completionStatus[date] = .noneCompleted
            }
        }
    }
}

struct DaySummary: View {
    let date: Date
    let status: WeeklyMedicationSummary.CompletionStatus

    var body: some View {
        VStack {
            Text(getDayShortName(from: date)).textCase(.uppercase)
                .font(.subheadline)
                .frame(minWidth: UIScreen.main.bounds.width/12)
            Text(getDayNumber(from: date))
                .frame(minWidth: UIScreen.main.bounds.width/12)
        }
        .padding(5)
        .foregroundColor(foregroundColorForStatus)
        .background(backgroundColorForStatus)
        .cornerRadius(10)
    }

    private var foregroundColorForStatus: Color {
        switch status {
        case .allCompleted, .partiallyCompleted:
            return .white
        case .noMedications, .noneCompleted:
            return Color("Background 2")
        }
    }

    private var backgroundColorForStatus: Color {
        switch status {
        case .allCompleted:
            return .green
        case .partiallyCompleted:
            return .yellow
        case .noneCompleted:
            return Color(hex: "e6e6eb")
        case .noMedications:
            return Color(hex: "e6e6eb")
        }
    }

    func getDayShortName(from date: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "EE"
        return dateFormatter.string(from: date)
    }

    func getDayNumber(from date: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "d"
        return dateFormatter.string(from: date)
    }
}

extension Date {
    var startOfWeek: Date {
        Calendar.current.dateComponents([.calendar, .yearForWeekOfYear, .weekOfYear], from: self).date!
    }

    var dayOfWeekShort: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEE"
        return formatter.string(from: self)
    }

    var dayOfMonth: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter.string(from: self)
    }
}

// Add this extension to Calendar
extension Calendar {
    func generateDates(
        inside interval: DateInterval,
        matching components: DateComponents
    ) -> [Date] {
        var dates: [Date] = []
        dates.append(interval.start)

        enumerateDates(
            startingAfter: interval.start,
            matching: components,
            matchingPolicy: .nextTime
        ) { date, _, stop in
            if let date = date {
                if date < interval.end {
                    dates.append(date)
                } else {
                    stop = true
                }
            }
        }

        return dates
    }
}

struct HomeView: View {
    @Binding var selectedTab: Tab
    @StateObject private var medicationManager = MedicationManager()
    @StateObject private var blogService = BlogService()
    @AppStorage("enrolledPathName") private var enrolledPathName: String = ""
    @AppStorage("enrolledDate") private var enrolledDateString: String = ""
    @AppStorage("latestHairDensity") private var latestHairDensity: Double = 0
    @AppStorage("latestHairLossLevel") private var latestHairLossLevel: String = "Unknown"
    @AppStorage("latestNorwoodLevel") private var latestNorwoodLevel: String = "Unknown"
    @AppStorage("latestAnalysisTimestamp") private var latestAnalysisTimestamp: Double = 0
    @State private var enrolledDays = 0
    @State private var hasLoaded = false
    @State private var selectedBlogPost: BlogPost?
    @State private var showBlogPostDetail = false
    @State private var showPayView = false
    @State private var subscriptionType: String = "unknown"
    @State private var isSubscribed: Bool = true
    @State private var showPastAnalyses = false
    @State private var hasAccessibleAnalysis = false
    
    var body: some View {
        ZStack {
            Color("Background").ignoresSafeArea()
            
            ScrollView {
                content
            }
        }
        .onAppear {
            if !hasLoaded {
                loadInitialData()
                hasLoaded = true
            }
            updateEnrolledDays()
            listenForPathChanges()
            listenForHairDensityChanges()
            if blogService.blogPosts.isEmpty {
                blogService.fetchBlogPosts()
            }
            fetchSubscriptionInfo()
            listenForAccessibleAnalysisChanges()
        }
        .sheet(item: $selectedBlogPost) { post in
            BlogPostDetail(post: post)
        }
        .sheet(isPresented: $showPayView) {
            PayView(
                onDismiss: {
                    showPayView = false
                },
                onPurchaseComplete: {
                    showPayView = false
                    fetchSubscriptionInfo() // Refresh subscription info after purchase
                },
                placementId: "homepage",
                currentStep: .constant(0)
            )
        }
        .sheet(isPresented: $showPastAnalyses) { // New sheet presentation
            PastAnalysesView()
        }
    }
    
    var content: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Show Upgrade box only for free users
            if !isSubscribed {
                UpgradeBox(showPayView: $showPayView)
                    .transition(.scale.combined(with: .opacity))
                    .padding(.horizontal)
            }

         

           

            VStack {
            
              
              ToolsView(selectedTab: $selectedTab)

                 if !enrolledPathName.isEmpty {
                VStack(alignment: .leading, spacing: 10) {
                    HStack {
                        Text("Day \(enrolledDays+1)")
                            .font(.subheadline)
                        Spacer()
                        Text("180 days")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }
                    
                    ProgressBar(progress: Double(enrolledDays+1) / 180.0)
                        .frame(height: 10)
                        .id(enrolledDays)
                    Text("You are on the \(enrolledPathName) path.")
                        .font(.footnote)
                        .fontWeight(.light)
                    Text("Remember! Hair treatment requires dedication!")
                        .font(.footnote)
                        .fontWeight(.ultraLight)
                }
                .padding(.vertical)
                .cornerRadius(10)
            }
            if hasAccessibleAnalysis {
                VStack {
                    Text("Current Status")
                        .font(.headline)
                        .padding(.vertical)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    HStack {
                        StatsView(label: "Density", content: String(format: "%.1f", latestHairDensity))
                        Spacer()
                        StatsView(label: "Hair Loss", content: latestHairLossLevel)
                        Spacer()
                        StatsView(label: "Norwood", content: latestNorwoodLevel)
                    }
                    HStack {
                        StatsView(label: "Treatment", content: "Stable")
                        Spacer()
                        StatsView(label: "Goal", content: goalNorwoodLevel)
                        Spacer()
                        StatsView(label: "Days", content: "\(enrolledDays+1)")
                    }
                }
                
                Button(action: {
                    showPastAnalyses = true
                }) {
                    Text("View all")
                        .font(.headline)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color("Background"))
                        .foregroundColor(Color("Background 4"))
                        .cornerRadius(10)
                }
                .padding(.horizontal)
            }
              
              
             

              VStack(alignment: .leading, spacing: 16) {
                  Text("Scientific researches on hair loss treatment")
                      .font(.headline)
                      .padding(.vertical)
                  
                  if blogService.isLoading {
                      ProgressView("Loading researchs...")
                  } else if blogService.blogPosts.isEmpty {
                      Text("No researchs available")
                  } else {
                      ForEach(blogService.blogPosts.prefix(4)) { post in
                          ArticlePost(title: post.title, imageUrl: post.coverImageUrl)
                              .onTapGesture {
                                  self.selectedBlogPost = post
                              }
                      }
                  }
              }
              .frame(maxWidth: .infinity)
            
                // New Button to show past analyses
               
                
            }
            .padding()
            
            WeeklyMedicationSummary(medicationManager: medicationManager, selectedTab: $selectedTab)
        }
    }
  struct ArticlePost: View {
      let title: String
      let imageUrl: String?
      
      var body: some View {
          HStack(spacing: 10) {
              if let imageUrl = imageUrl {
                  AsyncImage(url: URL(string: imageUrl)) { image in
                      image
                          .resizable()
                          .aspectRatio(contentMode: .fill)
                          .frame(width: 50, height: 50)
                          .clipShape(Circle())
                  } placeholder: {
                      ProgressView()
                          .frame(width: 50, height: 50)
                  }
              } else {
                  Image(systemName: "doc.text")
                      .resizable()
                      .aspectRatio(contentMode: .fit)
                      .frame(width: 50, height: 50)
                      .foregroundColor(.white)
                      .background(Color(hex: "feecba"))
                      .clipShape(Circle())
              }
          
              Text(title)
                  .font(.subheadline)
                  .lineLimit(2)
              Spacer()
              Image(systemName: "arrow.up.right")
                  .padding()
          }
          .padding(8)
          .frame(maxWidth: .infinity)
          .foregroundColor(.black)
          .background(Color(hex: "f8f8fc"))
          .clipShape(RoundedRectangle(cornerRadius: 40, style: .continuous))
      }
  }

    private func loadInitialData() {
        if enrolledPathName.isEmpty {
            fetchEnrolledPath()
        }
        if latestAnalysisTimestamp == 0 {
            fetchLatestHairDensityAnalysis()
        }
        medicationManager.loadMedications()
        medicationManager.loadAllCompletions()
    }

    private func updateEnrolledDays() {
        if let enrolledDate = ISO8601DateFormatter().date(from: enrolledDateString) {
            let calendar = Calendar.current
            let components = calendar.dateComponents([.day], from: enrolledDate, to: Date())
            self.enrolledDays = components.day ?? 0
        }
    }

    private func fetchEnrolledPath() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        db.collection("users").document(userId).collection("paths").getDocuments { snapshot, error in
            if let error = error {
                return
            }
            
            if let document = snapshot?.documents.first {
                updateEnrolledPathFromDocument(document)
            }
        }
    }

    private func listenForPathChanges() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        db.collection("users").document(userId).collection("paths")
            .addSnapshotListener { querySnapshot, error in
                guard let documents = querySnapshot?.documents else {
                    return
                }
                
                if let document = documents.first {
                    updateEnrolledPathFromDocument(document)
                } else {
                    // No path enrolled
                    self.enrolledPathName = ""
                    self.enrolledDateString = ""
                    self.enrolledDays = 0
                }
            }
    }

    private func updateEnrolledPathFromDocument(_ document: QueryDocumentSnapshot) {
        let pathName = document.data()["name"] as? String
        let enrolledDateTimestamp = document.data()["enrolledDate"] as? Timestamp
        
        if let pathName = pathName, let enrolledDate = enrolledDateTimestamp?.dateValue() {
            self.enrolledPathName = pathName
            self.enrolledDateString = ISO8601DateFormatter().string(from: enrolledDate)
            self.updateEnrolledDays()
        }
    }

    private func fetchLatestHairDensityAnalysis() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        db.collection("users").document(userId).collection("hair_density_analyses")
            .whereField("isAccessible", isEqualTo: true) // Add this line to filter accessible analyses
            .order(by: "timestamp", descending: true)
            .limit(to: 1)
            .getDocuments { snapshot, error in
                if let error = error {
                    return
                }
                
                if let document = snapshot?.documents.first {
                    updateLatestAnalysisFromDocument(document)
                }
            }
    }

    private func listenForHairDensityChanges() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        db.collection("users").document(userId).collection("hair_density_analyses")
            .whereField("isAccessible", isEqualTo: true) // Add this line to filter accessible analyses
            .order(by: "timestamp", descending: true)
            .limit(to: 1)
            .addSnapshotListener { querySnapshot, error in
                guard let documents = querySnapshot?.documents else {
                    return
                }
                
                if let document = documents.first {
                    updateLatestAnalysisFromDocument(document)
                }
            }
    }

    private func updateLatestAnalysisFromDocument(_ document: QueryDocumentSnapshot) {
        let data = document.data()
        self.latestHairDensity = data["finalHairDensity"] as? Double ?? 0
        self.latestHairLossLevel = data["hairLossLevel"] as? String ?? "Unknown"
        self.latestNorwoodLevel = data["norwoodLevel"] as? String ?? "Unknown"
        if let timestamp = (data["timestamp"] as? Timestamp)?.dateValue().timeIntervalSince1970 {
            self.latestAnalysisTimestamp = timestamp
        }
    }

    // Add this computed property to the HomeView struct
    private var goalNorwoodLevel: String {
        if let currentLevel = Int(latestNorwoodLevel.replacingOccurrences(of: "Norwood ", with: "")) {
            if currentLevel > 1 {
                return "Norwood \(currentLevel - 1)"
            } else {
                return "Keep Level"
            }
        }
        return "Improve"
    }

    // Add this function to fetch subscription info
    private func fetchSubscriptionInfo() {
        guard let userId = Auth.auth().currentUser?.uid else {
            return
        }

        // Sync with RevenueCat first (like main2), then read from Firebase
        Task {
            do {
                let customerInfo = try await Purchases.shared.customerInfo()
                let isPremium = customerInfo.entitlements["premium"]?.isActive ?? false

                // Update Firebase with current RevenueCat status
                await syncSubscriptionStatus(userId: userId, isPremium: isPremium)

                // Then read from Firebase for UI
                await MainActor.run {
                    readSubscriptionFromFirebase(userId: userId)
                }

            } catch {
                // Fallback to reading from Firebase
                readSubscriptionFromFirebase(userId: userId)
            }
        }
    }

    private func readSubscriptionFromFirebase(userId: String) {
        let db = Firestore.firestore()
        let subscriptionRef = db.collection("users").document(userId).collection("subscriptions").document("current_subscription")

        subscriptionRef.getDocument { (document, error) in
            if let document = document, document.exists {
                if let subscriptionType = document.data()?["subscriptionType"] as? String {
                    self.subscriptionType = subscriptionType
                    self.isSubscribed = subscriptionType != "free"
                }
            } else {
                self.isSubscribed = false
            }
        }
    }

    // Sync subscription status with Firebase (matches main2 approach)
    @MainActor
    private func syncSubscriptionStatus(userId: String, isPremium: Bool) async {
        let db = Firestore.firestore()
        let subscriptionRef = db.collection("users").document(userId)
            .collection("subscriptions").document("current_subscription")

        let subscriptionData: [String: Any] = [
            "subscriptionType": isPremium ? "premium" : "free",
            "analyzesLeft": isPremium ? 1000 : 0,
            "lastChecked": Timestamp(date: Date())
        ]

        do {
            try await subscriptionRef.setData(subscriptionData, merge: true)
        } catch {
            // Handle error silently
        }
    }

    private func listenForAccessibleAnalysisChanges() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        db.collection("users").document(userId).collection("hair_density_analyses")
            .whereField("isAccessible", isEqualTo: true)
            .order(by: "timestamp", descending: true)
            .limit(to: 1)
            .addSnapshotListener { querySnapshot, error in
                guard let documents = querySnapshot?.documents else {
                    return
                }
                
                if let document = documents.first {
                    self.updateLatestAnalysisFromDocument(document)
                    self.hasAccessibleAnalysis = true
                } else {
                    self.hasAccessibleAnalysis = false
                }
            }
    }
}

struct ProgressBar: View {
    var progress: Double
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                Rectangle()
                    .foregroundColor(Color.gray.opacity(0.3))
                
                Rectangle()
                    .fill(LinearGradient(gradient: Gradient(colors: [Color.blue, Color.purple]), startPoint: .leading, endPoint: .trailing))
                    .frame(width: min(CGFloat(self.progress) * geometry.size.width, geometry.size.width))
            }
            .cornerRadius(5)
        }
    }
}

struct StatsView: View {
    var label: String
    var content: String
    
    var body: some View {
        VStack {
          
          VStack {
            Text("\(content)")
                  .font(.subheadline)
                .fontWeight(.bold)
                .foregroundStyle(.black)

          }
        
            Text(label)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.gray)
        }    .font(.headline)
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(red: 0.965, green: 0.973, blue: 0.976))
        .cornerRadius(10)
    }
}

extension Date: Identifiable {
    public var id: String {
        return self.description
    }
}

struct DayMedicationsView: View {
    let date: Date
    @ObservedObject var medicationManager: MedicationManager
    @Binding var selectedTab: Tab
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                List {
                    ForEach(medicationsForDate) { medication in
                        Text(medication.name)
                            .strikethrough(isCompleted(medication))
                    }
                }
                
                Button(action: {
                    dismiss()
                    selectedTab = .search
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        NotificationCenter.default.post(name: Notification.Name("ScrollToDate"), object: date)
                    }
                }) {
                    Text("Go to medications")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .padding()
            }
            .navigationTitle(date.formatted(date: .long, time: .omitted))
        }
    }

    private var medicationsForDate: [MedicationDocument] {
        medicationManager.medications.filter { medication in
            medication.dates.contains { Calendar.current.isDate($0, inSameDayAs: date) }
        }
    }

    private func isCompleted(_ medication: MedicationDocument) -> Bool {
        medicationManager.completions.contains { completion in
            completion.medicationId == medication.id &&
            Calendar.current.isDate(completion.date, inSameDayAs: date) &&
            completion.isCompleted
        }
    }
}

struct UpgradeBox: View {
    @Binding var showPayView: Bool
    @State private var isAnimating = false
    @State private var title: String = "Get your deal now"
    @State private var subtitle: String = "Upgrade for exclusive benefits"
    @State private var buttonText: String = "Upgrade"
    
    var body: some View { 
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
                Text(subtitle)
                .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            Spacer()
            Button(action: {
                showPayView = true
            }) {
                Text(buttonText)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.white)
                    .foregroundColor(.blue)
                    .cornerRadius(20)
            }
            .scaleEffect(isAnimating ? 1.05 : 1.0)
            .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: isAnimating)
        }
        .padding()
        .background(Color.blue)
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        .onAppear {
            isAnimating = true
            fetchRemoteConfigValues()
        }
    }
    
    private func fetchRemoteConfigValues() {
        title = RemoteConfigManager.shared.getString(forKey: "upgrade_box_title")
        subtitle = RemoteConfigManager.shared.getString(forKey: "upgrade_box_subtitle")
        buttonText = RemoteConfigManager.shared.getString(forKey: "upgrade_box_button_text")
    }
}

#Preview {
    HomeView(selectedTab: .constant(.chat))
}
