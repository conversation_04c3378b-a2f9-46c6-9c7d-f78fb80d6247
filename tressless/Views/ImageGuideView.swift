//
//  ImageGuideView.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI

struct ImageGuideView: View {
    var body: some View {
        VStack {
            
                VStack {
                    Text("Correct Angle of Head")
                        .font(.headline)
                        .padding(.bottom, 5)
                    Image("correct_angle")
                        .resizable()
                        .scaledToFit()
                        .cornerRadius(10)
                        .frame(minWidth:  100 )
                  
                
                
            }
            
          
          VStack {
            Text("To get the most accurate results, it's crucial to capture your hair from the top angle. Make sure your hair is clearly visible, centered in the frame, and well-lit.")
                .font(.footnote)
                .fontWeight(.light)
            Text("Incorect Angles")
                .font(.headline)
                .padding(.bottom, 5)
            HStack {
               
                  VStack {
                 
                      Image("wrong_angle_1")
                          .resizable()
                          .scaledToFit()
                          .cornerRadius(10)
                  }
                  VStack {
                      Image("wrong_angle_2")
                          .resizable()
                          .scaledToFit()
                          .cornerRadius(10)
                  }
                  VStack {
                      Image("wrong_angle_3")
                          .resizable()
                          .scaledToFit()
                          .cornerRadius(10)
                  }
            }
          }
        }
        .padding()
    }
}

#Preview {
    ImageGuideView()
}
