//
//  HCard.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//



import SwiftUI

struct HCard: View {
    var section = courseSections[0]
    
    var body: some View {
        HStack(spacing: 20) {
            VStack(alignment: .leading, spacing: 8) {
                Text(section.title)
                .font(.title)
                    .frame(maxWidth: .infinity, alignment: .leading)
                Text(section.caption)
                .font(.caption)
            }
            Divider()
            section.image
        }
        .padding(20)
        .frame(maxWidth: .infinity, maxHeight: 150)
        .foregroundColor(.white)
        .background(section.color)
        .mask(RoundedRectangle(cornerRadius: 30, style: .continuous))
    }
}

#Preview {
    HCard()
}
