//
//  CustomButton.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI

struct CustomButton: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .foregroundColor(.white)
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color.black)
                .cornerRadius(10)
        }
        .padding(.horizontal)
    }
}
