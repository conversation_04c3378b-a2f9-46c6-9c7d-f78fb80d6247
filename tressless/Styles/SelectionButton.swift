//
//  SelectionButton.swift
//  tressless
//
//  Created by <PERSON> on 17.08.2024.
//

import SwiftUI

struct SelectionButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
            .foregroundColor(isSelected ? Color.white : Color("Background 4"))
                .padding()
                .frame(maxWidth: .infinity)
                .background(isSelected ? Color.black : Color("Background 4").opacity(0.1))
                .cornerRadius(10)
               
        }
        .padding(.horizontal)
    }
}

