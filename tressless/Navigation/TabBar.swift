//
//  TabBar.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI
import RiveRuntime

struct TabBar: View {
    @Binding var selectedTab: Tab // Use a binding


    var body: some View {
        VStack {
            Spacer() // Push the  to the bottom

            // Rive icons for tab
            HStack {
                ForEach(tabItems) { item in
                    Button {
                        try? item.icon.setInput("active", value: true)
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            try? item.icon.setInput("active", value: false)
                        }
                        withAnimation {
                            selectedTab = item.tab
                        }
                    } label: {
                        item.icon.view()
                            .frame(width: 36, height: 36)
                            .frame(maxWidth: .infinity)
                            .opacity(selectedTab == item.tab ? 1 : 0.5)
                            .background(
                                VStack {
                                    RoundedRectangle(cornerRadius: 2)
                                        .frame(width: selectedTab == item.tab ? 20 : 0, height: 4)
                                        .offset(y: -4)
                                        .opacity(selectedTab == item.tab ? 1 : 0)
                                    Spacer()
                                }
                            )
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .padding(12)
            .background(Color("Background 2").opacity(0.8))
            .background(.ultraThinMaterial)
            .mask(RoundedRectangle(cornerRadius: 26, style: .continuous))
            .shadow(color: Color("Background 2").opacity(0.3), radius: 20, x: 0, y: 20)
            .overlay(
                RoundedRectangle(cornerRadius: 26, style: .continuous)
                    .stroke(.linearGradient(colors: [.white.opacity(0.5), .white.opacity(0)], startPoint: .topLeading, endPoint: .bottomTrailing))
            )
            .padding(.horizontal, 24)
        }
    }
}


// MARK: - Tab Items

struct TabItem: Identifiable {
  var id = UUID()
  var icon: RiveViewModel
  var tab: Tab
}

var tabItems = [
  TabItem(icon: RiveViewModel(fileName: "icons", stateMachineName: "HOME_interactivity", artboardName: "HOME"), tab: .chat),
  TabItem(icon: RiveViewModel(fileName: "icons", stateMachineName: "BELL_Interactivity", artboardName: "BELL"), tab: .search),
  TabItem(icon: RiveViewModel(fileName: "icons", stateMachineName: "SEARCH_Interactivity", artboardName: "SEARCH"), tab: .timer),
  TabItem(icon: RiveViewModel(fileName: "icons", stateMachineName: "TIMER_Interactivity", artboardName: "TIMER"), tab: .bell),
  TabItem(icon: RiveViewModel(fileName: "icons", stateMachineName: "USER_Interactivity", artboardName: "USER"), tab: .user)
]

enum Tab: String {
  case chat
  case search
  case timer
  case bell
  case user
}

// MARK: - Tab Views

struct ChatView: View {
  var body: some View {
    NavigationView {
      Text("Chat View")
        .navigationTitle("Chat")
    }
  }
}

struct SearchView: View {

    var body: some View {
        NavigationView {
            MedicationView()
        }
    }
}

struct TimerView: View {
  var body: some View {
    NavigationView {
     BlogHome()
    }
  }
}

struct BellView: View {
    var body: some View {
        NavigationView {
            GeminiView(onCompletion: {
                print("Image upload completed")
            })
        }
    }
}

struct UserView: View {
    @Binding var selectedTab: Tab // Use a binding

    var body: some View {
        NavigationView {
            ProfileView()
        }
    }
}






