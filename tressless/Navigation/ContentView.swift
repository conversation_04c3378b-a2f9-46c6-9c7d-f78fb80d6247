//
//  ContentView.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI
import RiveRuntime
import Firebase

struct ContentView: View {
    @AppStorage("uid") var userId: String = ""
    @State private var selectedTab: Tab = .chat
    @AppStorage("showUserGuiding") private var showUserGuiding = true
    @State var show = false
    @State var isOpen = false
    @State private var isFirstLaunch = false
    @EnvironmentObject var deepLinkHandler: DeepLinkHandler // Add this line
    @AppStorage("subscriptionType") private var subscriptionType: String = "unknown"
    @AppStorage("isSubscribed") private var isSubscribed: Bool = false
    
    
    init() {
        fetchSubscriptionInfo()
    }
    

    var body: some View {
        ZStack {
            Color("Background").ignoresSafeArea()

            // 🎯 GENUINE FIX: Remove duplicate onboarding logic
            // RootView already handles authentication & onboarding
            // ContentView should only handle app content & user guidance
            if showUserGuiding {
                mainContent
            } else {
                mainContent
            }
        }
        .onAppear(perform: checkUserProgress)
        .environmentObject(deepLinkHandler) // Add this line
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("HandleMedicationNotification"))) { notification in
            if let userInfo = notification.userInfo,
               userInfo["medicationId"] != nil {
                // Navigate to medication reminder tab when notification is tapped
                selectedTab = .search
            }
        }
    }
    
    

    var mainContent: some View {
        ZStack {
            switch selectedTab {
            case .chat:
                HomeView(selectedTab: $selectedTab)
            case .search:
                SearchView()
            case .timer:
                TimerView()
            case .bell:
                BellView()
            case .user:
                UserView(selectedTab: $selectedTab)
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: 80)
        }
        .safeAreaInset(edge: .top) {
            Color.clear.frame(height: 104)
        }
        .mask(RoundedRectangle(cornerRadius: 30, style: .continuous))
        .rotation3DEffect(.degrees(isOpen ? 30 : 0), axis: (x: 0, y: -1, z: 0), perspective: 1)
        .offset(x: isOpen ? 265 : 0)
        .scaleEffect(isOpen ? 0.9 : 1)
        .scaleEffect(show ? 0.92 : 1)
        .ignoresSafeArea()
        .overlay(
            TabBar(selectedTab: $selectedTab)
                .offset(y: -24)
                .background(
                    LinearGradient(colors: [Color("Background").opacity(0), Color("Background")], startPoint: .top, endPoint: .bottom)
                        .frame(height: 150)
                        .frame(maxHeight: .infinity, alignment: .bottom)
                        .allowsHitTesting(false)
                )
                .ignoresSafeArea()
                .offset(y: isOpen ? 300 : 0)
                .offset(y: show ? 200 : 0)
        )
    }
    
    // Add this function to fetch subscription info
    private func fetchSubscriptionInfo() {
        guard let userId = Auth.auth().currentUser?.uid else {
            return
        }
        
        let db = Firestore.firestore()
        let subscriptionRef = db.collection("users").document(userId).collection("subscriptions").document("current_subscription")
        
        subscriptionRef.getDocument { (document, error) in
            if let document = document, document.exists {
                if let subscriptionType = document.data()?["subscriptionType"] as? String {
                    self.subscriptionType = subscriptionType
                    self.isSubscribed = subscriptionType != "free"
                }
            } else {
                self.isSubscribed = false
            }
        }
    }

    private func checkUserProgress() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        let db = Firestore.firestore()

        let dispatchGroup = DispatchGroup()
        var hasImages = false
        var hasPath = false
        var hasAnalysis = false

        dispatchGroup.enter()
        db.collection("users").document(userId).collection("images").getDocuments { (snapshot, error) in
            hasImages = snapshot?.isEmpty == false
            dispatchGroup.leave()
        }

        dispatchGroup.enter()
        db.collection("users").document(userId).collection("paths").getDocuments { (snapshot, error) in
            hasPath = snapshot?.isEmpty == false
            dispatchGroup.leave()
        }

        dispatchGroup.enter()
        db.collection("users").document(userId).collection("hair_density_analyses").getDocuments { (snapshot, error) in
            hasAnalysis = snapshot?.isEmpty == false
            dispatchGroup.leave()
        }

        dispatchGroup.notify(queue: .main) {
            showUserGuiding = !(hasImages && hasPath && hasAnalysis)
        }
    }
}

extension UIViewController {
    func setStatusBarStyle(_ style: UIStatusBarStyle) {
        if let statusBar = UIApplication.shared.value(forKey: "statusBar") as? UIView {
            statusBar.backgroundColor = style == .lightContent ? UIColor.black : .white
            statusBar.setValue(style == .lightContent ? UIColor.white : .black, forKey: "foregroundColor")
        }
    }
}

#Preview {
    ContentView()
}
