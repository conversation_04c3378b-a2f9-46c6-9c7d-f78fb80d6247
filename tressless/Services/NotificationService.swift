//
//  NotificationService.swift
//  tressless
//
//  Created for FCM Integration
//

import Foundation
import FirebaseMessaging
import FirebaseFirestore
import FirebaseAuth
import UserNotifications

class NotificationService: ObservableObject {
    static let shared = NotificationService()
    private var pendingFCMToken: String?

    private init() {}

    func requestPermissionAndSetupFCM() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                if granted {
                    debugPrint("✅ Notification permission granted")
                    AnalyticsInfoLogger.shared.logEvent("Notification_Permission_Granted")

                    // Register for remote notifications
                    UIApplication.shared.registerForRemoteNotifications()

                    // Get FCM token
                    self.getFCMToken()
                } else {
                    debugPrint("❌ Notification permission denied: \(error?.localizedDescription ?? "Unknown error")")
                    AnalyticsInfoLogger.shared.logEvent("Notification_Permission_Denied")
                }
            }
        }
    }

    private func getFCMToken() {
        Messaging.messaging().token { token, error in
            if let error = error {
                debugPrint("❌ Error fetching FCM registration token: \(error.localizedDescription)")
            } else if let token = token {
                debugPrint("✅ FCM registration token: \(token)")
                debugPrint("🔑 COPY THIS TOKEN: \(token)")
                self.saveFCMTokenToFirestore(token: token)
            }
        }
    }

    private func saveFCMTokenToFirestore(token: String) {
        guard let userId = Auth.auth().currentUser?.uid else {
            debugPrint("⏳ No authenticated user - storing token for later")
            pendingFCMToken = token
            return
        }

        let db = Firestore.firestore()
        let userDoc = db.collection("users").document(userId)

        let tokenData: [String: Any] = [
            "fcmToken": token,
            "lastUpdated": Timestamp(date: Date()),
            "platform": "iOS",
            "lastActivityAt": Timestamp(date: Date()) // Update activity when token is saved (user opened app)
        ]

        userDoc.setData(tokenData, merge: true) { error in
            if let error = error {
                debugPrint("❌ Error saving FCM token: \(error.localizedDescription)")
            } else {
                debugPrint("✅ FCM token saved successfully for user: \(userId)")
                AnalyticsInfoLogger.shared.logEvent("FCM_Token_Saved", properties: ["userId": userId])
                self.pendingFCMToken = nil // Clear pending token
            }
        }
    }

    // Call this when user signs in to save any pending token
    func savePendingTokenForCurrentUser() {
        guard let token = pendingFCMToken else { return }
        saveFCMTokenToFirestore(token: token)
    }

    // Call this when user signs in/out to update token
    func updateFCMTokenForCurrentUser() {
        getFCMToken()
    }
}
