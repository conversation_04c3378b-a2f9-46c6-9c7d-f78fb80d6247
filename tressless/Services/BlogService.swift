//
//  BlogService.swift
//  tressless
//
//  Created by <PERSON> on 27.08.2024.
//

import Foundation

class BlogService: ObservableObject {
    @Published var blogPosts: [BlogPost] = []
    @Published var isLoading: Bool = false
    
    private let graphQLEndpoint = "https://gql.hashnode.com/"
    
    func fetchBlogPosts() {
        isLoading = true
        let query = """
        query {
          user(username: "researchs") {
            publications(first: 50) {
              edges {
                node {
                  id
                  posts(first: 50) {
                    edges {
                      node {
                        id
                        title
                        brief
                        slug
                        coverImage {
                          url
                          attribution
                          photographer
                        }
                        content {
                          markdown
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        """
        
        let body = ["query": query]
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: body) else {
            print("Error: Cannot create JSON from body")
            return
        }
        
        var request = URLRequest(url: URL(string: graphQLEndpoint)!)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            if let error = error {
                print("Error: \(error.localizedDescription)")
                return
            }
            
            guard let data = data else {
                print("Error: No data received")
                return
            }
            
            do {
                let decoder = JSONDecoder()
                let blogResponse = try decoder.decode(BlogResponse.self, from: data)
                
                DispatchQueue.main.async {
                    self?.blogPosts = blogResponse.data.user.publications.edges.first?.node.posts.edges.map { edge in
                        let post = BlogPost(
                            title: edge.node.title,
                            brief: edge.node.brief,
                            slug: edge.node.slug,
                            coverImageUrl: edge.node.coverImage?.url,
                            coverImageAttribution: edge.node.coverImage?.attribution,
                            coverImagePhotographer: edge.node.coverImage?.photographer,
                            content: edge.node.content.markdown
                        )
                        return post
                    } ?? []
                    print("Total blog posts fetched: \(self?.blogPosts.count)")
                    self?.isLoading = false
                }
            } catch {
                print("Error decoding JSON: \(error.localizedDescription)")
            }
        }.resume()
    }
}
