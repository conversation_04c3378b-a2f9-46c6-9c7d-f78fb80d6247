//
//  ImageService.swift
//  tressless
//
//  Created by <PERSON> on 6.08.2024.
//


import Foundation
import SwiftUI
import FirebaseStorage
import FirebaseFirestore
import FirebaseAuth

class ImageService: ObservableObject {
    @Published var images: [UIImage] = [] // Make images property published
    private let fileManager = LocalFileManager.instance
    private let folderName = "hair_images"
    private let area: Area
    
    init(area: Area) {
        self.area = area
        fetchImages()
        
    }
    
    private func fetchImages() {
        // Check local cache first
        if let localImages = getImagesFromLocalCache() {
            images = localImages
            
        } else {
            // Fetch images from Firebase and cache them locally
            fetchImagesFromFirebase()
        }
    }
    
    private func getImagesFromLocalCache() -> [UIImage]? {
        var localImages: [UIImage] = []
        for index in 0..<10 {
            let imageName = "\(area.rawValue)_\(index)"
            if let image = fileManager.getImage(imageName: imageName, folderName: folderName) {
                localImages.append(image)
            }
        }
        return localImages.isEmpty ? nil : localImages
    }
     func fetchImagesFromFirebase() {
        // Fetch images from Firebase and cache them locally
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        let query = db.collection("users").document(userId).collection("hair_treatment_photos")
            .whereField("area", isEqualTo: area.rawValue)
        
        query.addSnapshotListener { (snapshot, error) in
            if let error = error {
                print("Error fetching images: \(error.localizedDescription)")
                return
            }
            
            guard let documents = snapshot?.documents else { return }
            
            var fetchedImages: [UIImage] = []
            for (index, document) in documents.enumerated() {
                if let urlString = document.data()["url"] as? String,
                   let url = URL(string: urlString),
                   let data = try? Data(contentsOf: url),
                   let image = UIImage(data: data) {
                    fetchedImages.append(image)
                    let imageName = "\(self.area.rawValue)_\(index)"
                    self.fileManager.saveImage(image: image, imageName: imageName, folderName: self.folderName)
                }
            }
            
            DispatchQueue.main.async {
                self.images = fetchedImages
            }
        }
    }
}

