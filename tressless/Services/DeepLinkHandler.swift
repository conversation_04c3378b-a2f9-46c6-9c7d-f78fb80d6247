//
//  DeepLinkHandler.swift
//  tressless
//
//  Created by <PERSON> on 16.09.2024.
//

import SwiftUI

enum DeepLinkAction: Equatable {
    case openClassifyView
    case openMedicationReminder(medicationId: String)
    // Add more actions as needed
}

class DeepLinkHandler: ObservableObject {
    @Published var action: DeepLinkAction?
    
    func handleDeepLink(_ url: URL) {
        guard url.scheme == "follicleai" else { return }
        
        switch url.host {
        case "classify":
            action = .openClassifyView
        case "medication":
            if let medicationId = url.queryParameters["id"] {
                action = .openMedicationReminder(medicationId: medicationId)
            }
        // Add more cases for other deep link actions
        default:
            break
        }
    }

    // Handle notification data for medication reminders
    func handleNotificationData(_ userInfo: [AnyHashable: Any]) {
        if let medicationId = userInfo["medicationId"] as? String {
            action = .openMedicationReminder(medicationId: medicationId)
        }
    }
}

extension URL {
    var queryParameters: [String: String] {
        guard let components = URLComponents(url: self, resolvingAgainstBaseURL: true),
              let queryItems = components.queryItems else {
            return [:]
        }

        var parameters: [String: String] = [:]
        for item in queryItems {
            parameters[item.name] = item.value
        }
        return parameters
    }
}

