//  PayView.swift
//  Created by <PERSON> on 6.08.2024.

import SwiftUI
import RevenueCat
import RevenueCatUI
import Firebase
import FirebaseFirestore



struct PayView: View {
    var onDismiss: () -> Void
    var onPurchaseComplete: () -> Void
    let placementId: String
    @Binding var currentStep: Int

    @State private var currentOffering: Offering?
    @State private var isLoading = true

    var body: some View {
        ZStack {
            if isLoading {
                VStack {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("Loading...")
                        .padding(.top)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black.opacity(0.4))
                .edgesIgnoringSafeArea(.all)
            } else if let offering = currentOffering {
                RevenueCatUI.PaywallView()
                    .onPurchaseCompleted { customerInfo in
                        AnalyticsInfoLogger.shared.logEvent("Purchase_Completed")
                        updateSubscriptionStatus(customerInfo: customerInfo)
                        onPurchaseComplete()
                    }
                    .onPurchaseFailure { error in
                        // RevenueCat shows error to user automatically
                        // We just log it for analytics
                        AnalyticsInfoLogger.shared.logEvent("Purchase_Failed", properties: [
                            "error_code": (error as NSError).code,
                            "error_description": error.localizedDescription,
                            "placement": placementId
                        ])
                    }
                    .onRestoreCompleted { customerInfo in
                        let hasPremium = customerInfo.entitlements["premium"]?.isActive == true
                        if hasPremium {
                            AnalyticsInfoLogger.shared.logEvent("Purchase_Restored")
                            updateSubscriptionStatus(customerInfo: customerInfo)
                            onPurchaseComplete()
                        }
                    }
                    .onRequestedDismissal {
                        onDismiss()
                    }
            }
        }
        .onAppear {
            loadPaywall()
            AnalyticsInfoLogger.shared.logEvent("Paywall_View_Shown")
        }
    }

    private func loadPaywall() {
        // Ensure user is identified with RevenueCat
        if let userId = Auth.auth().currentUser?.uid {
            Task {
                do {
                    let _ = try await Purchases.shared.logIn(userId)
                } catch {
                    // Handle login error silently
                }
                await loadOfferings()
            }
        } else {
            Task {
                await loadOfferings()
            }
        }
    }

    @MainActor
    private func loadOfferings() async {
        do {
            let offerings = try await Purchases.shared.offerings()

            // Try to get the default offering first, fallback to current
            if let specificOffering = offerings.offering(identifier: "default") {
                currentOffering = specificOffering
            } else {
                currentOffering = offerings.current
            }

            isLoading = false
        } catch {
            isLoading = false
        }
    }

    private func updateSubscriptionStatus(customerInfo: CustomerInfo) {
        let isPremium = customerInfo.entitlements["premium"]?.isActive == true

        // If user is logged in, update Firebase immediately
        if let userId = Auth.auth().currentUser?.uid {
            updateFirebaseSubscription(userId: userId, isPremium: isPremium)
        } else {
            // For anonymous users, RevenueCat handles the purchase automatically
            // We'll sync with Firebase when they create an account
            AnalyticsInfoLogger.shared.logEvent("Anonymous_Purchase_Completed", properties: [
                "subscription_type": isPremium ? "premium" : "free"
            ])
        }
    }

    private func updateFirebaseSubscription(userId: String, isPremium: Bool) {
        let db = Firestore.firestore()
        let subscriptionRef = db.collection("users").document(userId).collection("subscriptions").document("current_subscription")

        let subscriptionData: [String: Any] = [
            "subscriptionType": isPremium ? "premium" : "free",
            "analyzesLeft": isPremium ? 1000 : 0,
            "startDate": Timestamp(date: Date()),
            "lastUpdated": Timestamp(date: Date())
        ]

        subscriptionRef.setData(subscriptionData, merge: true) { error in
            if error == nil {
                AnalyticsInfoLogger.shared.logEvent("Subscription_Updated", properties: [
                    "subscription_type": isPremium ? "premium" : "free",
                    "analyses_left": isPremium ? 1000 : 0,
                    "user_id": userId
                ])
            }
        }
    }

}

struct PayView_Previews: PreviewProvider {
    static var previews: some View {
        PayView(onDismiss: {}, onPurchaseComplete: {}, placementId: "onboarding", currentStep: .constant(0))
    }
}
