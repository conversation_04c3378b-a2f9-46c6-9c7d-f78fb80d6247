{"appPolicies": {"eula": "", "policies": [{"locale": "en_US", "policyText": "", "policyURL": ""}]}, "identifier": "7E7C5A21", "nonRenewingSubscriptions": [], "products": [{"displayPrice": "1.99", "familyShareable": false, "internalID": "6636465456", "localizations": [{"description": "hair analyze", "displayName": "hair analyze", "locale": "en_US"}], "productID": "hair.analyze.count", "referenceName": "Hair Analyze Count", "type": "Consumable"}], "settings": {"_applicationInternalID": "6633411204", "_developerTeamID": "RAMRF7V47C", "_failTransactionsEnabled": false, "_lastSynchronizedDate": 745277196.434558, "_locale": "en_US", "_storefront": "USA", "_storeKitErrors": [{"current": null, "enabled": false, "name": "Load Products"}, {"current": null, "enabled": false, "name": "Purchase"}, {"current": null, "enabled": false, "name": "Verification"}, {"current": null, "enabled": false, "name": "App Store Sync"}, {"current": null, "enabled": false, "name": "Subscription Status"}, {"current": null, "enabled": false, "name": "App Transaction"}, {"current": null, "enabled": false, "name": "Manage Subscriptions Sheet"}, {"current": null, "enabled": false, "name": "Refund Request Sheet"}, {"current": null, "enabled": false, "name": "Offer Code Redeem Sheet"}]}, "subscriptionGroups": [{"id": "21525215", "localizations": [], "name": "premium", "subscriptions": [{"adHocOffers": [], "codeOffers": [], "displayPrice": "14.99", "familyShareable": false, "groupNumber": 1, "internalID": "6636465443", "introductoryOffer": null, "localizations": [{"description": "Become a premium member", "displayName": "Premium Monthly", "locale": "en_US"}], "productID": "LazuriteX.tressless", "recurringSubscriptionPeriod": "P1M", "referenceName": "Premium Monthly", "subscriptionGroupID": "21525215", "type": "RecurringSubscription", "winbackOffers": []}, {"adHocOffers": [], "codeOffers": [], "displayPrice": "5.99", "familyShareable": false, "groupNumber": 3, "internalID": "6636465710", "introductoryOffer": null, "localizations": [{"description": "Become weekly premium member", "displayName": "Premium Weekly", "locale": "en_US"}], "productID": "LazuriteX.tressless.premium.weekly", "recurringSubscriptionPeriod": "P1W", "referenceName": "Premium Weekly", "subscriptionGroupID": "21525215", "type": "RecurringSubscription", "winbackOffers": []}, {"adHocOffers": [], "codeOffers": [], "displayPrice": "49.99", "familyShareable": false, "groupNumber": 2, "internalID": "6636465566", "introductoryOffer": null, "localizations": [{"description": "Become a yearly member", "displayName": "Premium Yearly", "locale": "en_US"}], "productID": "LazuriteX.tressless.premium.yearly", "recurringSubscriptionPeriod": "P1Y", "referenceName": "Premium Yearly", "subscriptionGroupID": "21525215", "type": "RecurringSubscription", "winbackOffers": []}]}], "version": {"major": 4, "minor": 0}}