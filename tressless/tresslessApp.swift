//
//  tresslessApp.swift
//  tressless
//
//  Created by <PERSON> on 6.08.2024.

import SwiftUI
import Swift<PERSON>F<PERSON>baseAuth
import Firebase
import FirebaseCore
import FirebaseFirestore
import RevenueCat
import RevenueCatUI
import UserNotifications
import FirebaseMessaging
import FirebaseRemoteConfig
import UIKit
import os
let db = Firestore.firestore()

@main
struct tresslessApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var blogService = BlogService()
    @StateObject private var deepLinkHandler = DeepLinkHandler()
    @StateObject private var remoteConfig = RemoteConfigManager.shared
    @AppStorage("uid") var userId: String = ""
    @AppStorage("tempUserEmail") private var tempUserEmail: String?
    @AppStorage("tempUserPassword") private var tempUserPassword: String?
    
    func setupUser() {
        Auth.auth().addStateDidChangeListener { _, user in
            if let user = user, !user.uid.isEmpty {
                self.userId = user.uid
                AnalyticsInfoLogger.shared.setUserId(user.uid)

                // Update FCM token for current user
                NotificationService.shared.updateFCMTokenForCurrentUser()
                // Save any pending FCM token
                NotificationService.shared.savePendingTokenForCurrentUser()

                // Check if this is a temporary account
                let db = Firestore.firestore()
                db.collection("users").document(user.uid).getDocument { (document, error) in
                    if let document = document, document.exists {
                        let isTemporary = document.data()?["isTemporary"] as? Bool ?? false

                        // Identify user in RevenueCat and sync subscription status
                        Task {
                            do {
                                let _ = try await Purchases.shared.logIn(user.uid)

                                // Sync subscription status on app launch (like main2)
                                await self.syncSubscriptionStatus(userId: user.uid)
                            } catch {
                                // Handle RevenueCat login error silently
                            }
                        }

                        var attributes: [String: Any] = ["isTemporary": isTemporary]
                        if !isTemporary, let email = user.email {
                            attributes["email"] = email
                        }
                        AnalyticsInfoLogger.shared.identifyUser(userId: user.uid, traits: attributes)
                    }
                }
            } else {
                self.userId = ""
                AnalyticsInfoLogger.shared.setUserId("")

                // RevenueCat logout is handled automatically

                AnalyticsInfoLogger.shared.clearIdentify()

                // Clear temporary user credentials when logging out
                self.tempUserEmail = nil
                self.tempUserPassword = nil
            }
        }
    }

    func checkSubscriptionStatus(userId: String) {
        // NOT USED - RevenueCat handles this automatically
    }

    func updateFirebaseSubscriptionStatus(userId: String, isPremium: Bool) {
        let db = Firestore.firestore()
        let subscriptionRef = db.collection("users").document(userId).collection("subscriptions").document("current_subscription")

        var subscriptionData: [String: Any] = [
            "subscriptionType": isPremium ? "premium" : "free",
            "lastUpdated": Timestamp(date: Date())
        ]

        if isPremium {
            subscriptionData["analyzesLeft"] = 100
            subscriptionData["analysesUsed"] = 0
        } else {
            subscriptionData["analyzesLeft"] = 0
        }

        subscriptionRef.setData(subscriptionData, merge: true) { error in
            // Handle error silently
        }
    }

    // Sync subscription status with Firebase (matches main2 approach)
    @MainActor
    func syncSubscriptionStatus(userId: String) async {
        do {
            let customerInfo = try await Purchases.shared.customerInfo()
            let isPremium = customerInfo.entitlements["premium"]?.isActive ?? false

            let db = Firestore.firestore()
            let subscriptionRef = db.collection("users").document(userId)
                .collection("subscriptions").document("current_subscription")

            let subscriptionData: [String: Any] = [
                "subscriptionType": isPremium ? "premium" : "free",
                "analyzesLeft": isPremium ? 1000 : 0,
                "lastChecked": Timestamp(date: Date())
            ]

            try await subscriptionRef.setData(subscriptionData, merge: true)

            AnalyticsInfoLogger.shared.logEvent("Subscription_Status_Synced", properties: [
                "subscription_type": isPremium ? "premium" : "free",
                "analyses_left": isPremium ? 1000 : 0
            ])

        } catch {
            AnalyticsInfoLogger.shared.logEvent("Subscription_Sync_Failed", properties: [
                "error": error.localizedDescription
            ])
        }
    }

    var body: some Scene {
        WindowGroup {
            RootView()
                .environment(\.auth, AuthManager(configuration: .firebase))
                .environmentObject(blogService)
                .environmentObject(deepLinkHandler)
                .environmentObject(remoteConfig)
                .environmentObject(AnalyticsInfoLogger.shared)
                .onAppear {
                    // Configure RevenueCat FIRST (no dependencies, prevents crashes)
                    setupRevenueCat()

                    // Now safe to setup user (can use RevenueCat functions)
                    setupUser()

                    // Remote Config can happen in parallel (no rush)
                    fetchRemoteConfig()

                    // Other services
                    blogService.fetchBlogPosts()
                    NotificationService.shared.requestPermissionAndSetupFCM()
                }
                .onOpenURL { url in
                    deepLinkHandler.handleDeepLink(url)
                }
        }
    }

    // Configure RevenueCat immediately on app launch (no dependencies)
    func setupRevenueCat() {
        Purchases.configure(withAPIKey: "appl_EKcBjVOpurKGbamJqjCpvewbnFl")
    }

    func fetchRemoteConfig() {
        RemoteConfigManager.shared.fetchConfig { error in
            // Handle config fetch result silently
        }
    }
}

class AnalyticsInfoLogger: ObservableObject {
    static let shared = AnalyticsInfoLogger()

    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.app.analytics", category: "Analytics")

    // Private initializer to ensure singleton usage
    private init() {
        logger.info("Logger initialized")
    }

    func logEvent(_ eventType: String, properties: [String: Any]? = nil) {
        logger.info("Event tracked: \(eventType, privacy: .public) and properties: \(String(describing: properties), privacy: .public)")
    }

    func setUserProperties(_ properties: [String: Any]) {
        logger.debug("User properties set and properties: \(String(describing: properties), privacy: .public)")
    }

    func setUserId(_ userId: String) {
        logger.info("User ID set: \(userId, privacy: .public)")
    }

    func getDeviceId() -> String {
        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString
        logger.debug("Device ID retrieved: \(deviceId, privacy: .public)")
        return deviceId
    }

    func identifyUser(userId: String, traits: [String: Any]?) {
        if let traits = traits {
            logger.info("User: \(userId, privacy: .public) identified with traits: \(String(describing: traits), privacy: .public)")
        } else {
            logger.info("User: \(userId, privacy: .public) identified with no traits")
        }
    }

    func clearIdentify() {
        logger.info("identity cleared")
    }

    func trackSubscriptionCreated(productTitle: String, analyzesAdded: Int, subscriptionType: String) {
        let properties: [String: Any] = [
            "plan_name": productTitle,
            "analyses_added": analyzesAdded,
            "subscription_type": subscriptionType
        ]

        logEvent("Subscription_Created", properties: properties)
        logger.info("Subscription created: \(productTitle, privacy: .public), type: \(subscriptionType, privacy: .public)")
    }

    func trackSubscriptionUpdated(productTitle: String, analyzesAdded: Int, totalAnalyzesLeft: Int, subscriptionType: String) {
        let properties: [String: Any] = [
            "plan_name": productTitle,
            "analyses_added": analyzesAdded,
            "total_analyses_left": totalAnalyzesLeft,
            "subscription_type": subscriptionType
        ]

        logEvent("Subscription_Updated", properties: properties)
        logger.info("Subscription updated: \(productTitle, privacy: .public), type: \(subscriptionType, privacy: .public)")
    }

    func trackNewSubscriptionCreated(isPremium: Bool) {
        let properties: [String: Any] = [
            "type": isPremium ? "premium" : "free"
        ]

        logEvent("New_Subscription_Created", properties: properties)
        logger.info("New subscription created: \(isPremium ? "premium" : "free", privacy: .public)")
    }

    func trackHairDensityAnalysisSaved(finalHairDensity: Double, hairLossLevel: String, norwoodLevel: String, isAccessible: Bool, hasImage: Bool) {
        let properties: [String: Any] = [
            "finalHairDensity": finalHairDensity,
            "hairLossLevel": hairLossLevel,
            "norwoodLevel": norwoodLevel,
            "isAccessible": isAccessible,
            "hasImage": hasImage
        ]

        logEvent("Hair_Density_Analysis_Saved", properties: properties)
        logger.info("Hair density analysis saved: level \(hairLossLevel, privacy: .public), accessible: \(isAccessible, privacy: .public)")
    }

    func trackSubscriptionFetched(subscriptionType: String) {
        let properties: [String: Any] = [
            "type": subscriptionType
        ]

        logEvent("Subscription_Fetched", properties: properties)
        logger.info("Subscription fetched: \(subscriptionType, privacy: .public)")
    }

    func registerDeviceToken(_ token: String) {
        logger.info("Device token registered (note: CustomerIO integration removed)")
    }

    // Generic method for logging debug messages
    func debug(_ message: String, metadata: [String: Any]? = nil) {
        logger.debug("\(message, privacy: .public)")

        // Optionally log to analytics as well
        if let metadata = metadata {
            logEvent("Debug_\(message)", properties: metadata)
        }
    }

    // Log app lifecycle events
    func logAppLaunch() {
        logger.info("Application launched")
        logEvent("App_Launched")
    }

    func logAppBackground() {
        logger.info("Application moved to background")
        logEvent("App_Backgrounded")
    }

    func logAppForeground() {
        logger.info("Application moved to foreground")
        logEvent("App_Foregrounded")
    }

    func logAppTermination() {
        logger.info("Application terminated")
        logEvent("App_Terminated")
    }

    // Log screen views
    func logScreenView(screenName: String, screenClass: String? = nil) {
        let properties: [String: Any] = [
            "screen_name": screenName,
            "screen_class": screenClass ?? NSStringFromClass(UIViewController.self)
        ]

        logger.info("Screen viewed: \(screenName, privacy: .public)")
        logEvent("Screen_View", properties: properties)
    }
}

class AppDelegate: NSObject, UIApplicationDelegate, MessagingDelegate, UNUserNotificationCenterDelegate {
    let gcmMessageIDKey = "gcm.message_id"

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil) -> Bool {
        FirebaseApp.configure()

        // Fetch Remote Config values
        RemoteConfigManager.shared.fetchConfig { error in
            if error == nil {
                self.setupServices()
            }
        }

        Messaging.messaging().delegate = self

        // Set up the UNUserNotificationCenter delegate
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().delegate = self
        }

        return true
    }

    func setupServices() {
        // RevenueCat configured in tresslessApp.setupRevenueCat()
        // This function can be used for other Remote Config dependent services
    }

    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        let tokenParts = deviceToken.map { data in String(format: "%02.2hhx", data) }
        let token = tokenParts.joined()

        // Set APNs token for FCM
        Messaging.messaging().apnsToken = deviceToken

        // Log the token registration
        AnalyticsInfoLogger.shared.registerDeviceToken(token)
    }

    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        // Handle registration failure silently
    }

    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any],
                     fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        completionHandler(.newData)
    }

    func userNotificationCenter(_ center: UNUserNotificationCenter,
                                willPresent notification: UNNotification,
                                withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        completionHandler([[.banner, .badge, .sound]])
    }

    func userNotificationCenter(_ center: UNUserNotificationCenter,
                                didReceive response: UNNotificationResponse,
                                withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo

        // Handle medication reminder notifications
        DispatchQueue.main.async {
            NotificationCenter.default.post(
                name: Notification.Name("HandleMedicationNotification"),
                object: nil,
                userInfo: userInfo
            )
        }

        completionHandler()
    }

    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        let dataDict: [String: String] = ["token": fcmToken ?? ""]
        NotificationCenter.default.post(
            name: Notification.Name("FCMToken"),
            object: nil,
            userInfo: dataDict
        )

        // Save token to Firestore automatically
        if let token = fcmToken {
            saveFCMTokenToFirestore(token: token)
        }
    }

    private func saveFCMTokenToFirestore(token: String) {
        let db = Firestore.firestore()

        if let userId = Auth.auth().currentUser?.uid {
            // Authenticated user - save to user document
            let userDoc = db.collection("users").document(userId)
            let tokenData: [String: Any] = [
                "fcmToken": token,
                "lastUpdated": Timestamp(date: Date()),
                "platform": "iOS",
                "lastActivityAt": Timestamp(date: Date()) // Update activity when token is saved (user opened app)
            ]

            userDoc.setData(tokenData, merge: true) { error in
                if error == nil {
                    AnalyticsInfoLogger.shared.logEvent("FCM_Token_Saved", properties: ["userId": userId])

                    // Check if this token was previously anonymous and migrate it
                    self.migrateAnonymousTokenIfExists(token: token, userId: userId)
                }
            }
        } else {
            // Anonymous user - save to anonymous tokens collection
            let anonymousTokenData: [String: Any] = [
                "fcmToken": token,
                "createdAt": Timestamp(date: Date()),
                "platform": "iOS",
                "deviceId": UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString,
                "status": "anonymous"
            ]

            // Encode token to make it a valid Firestore document ID (replace : with _)
            let encodedToken = token.replacingOccurrences(of: ":", with: "_")
            db.collection("anonymousTokens").document(encodedToken).setData(anonymousTokenData) { error in
                if error == nil {
                    AnalyticsInfoLogger.shared.logEvent("Anonymous_FCM_Token_Saved", properties: ["token": token])
                }
            }
        }
    }

    private func migrateAnonymousTokenIfExists(token: String, userId: String) {
        let db = Firestore.firestore()

        // Check if this token exists in anonymousTokens collection
        // Encode token to make it a valid Firestore document ID (replace : with _)
        let encodedToken = token.replacingOccurrences(of: ":", with: "_")
        db.collection("anonymousTokens").document(encodedToken).getDocument { (document, error) in
            if let document = document, document.exists {
                // Delete from anonymous collection since it's now associated with a user
                document.reference.delete { error in
                    if error == nil {
                        AnalyticsInfoLogger.shared.logEvent("Anonymous_Token_Migrated", properties: ["userId": userId])
                    }
                }
            }
        }
    }
}

// RevenueCat delegate handling is built-in