//
//  Classify.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//
import SwiftUI
import UIKit
import FirebaseFirestore
import FirebaseAuth
import Alamofire

struct APIHairAnalysisResult: Codable {
    let hair_density: Double
    let class_confidences: [ClassConfidence]
    let haircountperarea: [String: Double]
    let norwoodLevel: String
    let hairLossLevel: String
    let segmentation_confidence: Double
    let error: String?
    var isAccessible: Bool? // Change this line from 'let' to 'var'
}

struct ClassConfidence: Codable {
    let `class`: Int
    let confidence: Double
    let name: String
}

class NetworkManager: ObservableObject {
    private let logger: AnalyticsInfoLogger
    private let remoteConfig: RemoteConfigManager
    
    init() {
        self.remoteConfig = RemoteConfigManager.shared
        self.logger = AnalyticsInfoLogger.shared
    }
    
    func analyzeHair(image: UIImage, completion: @escaping (Result<APIHairAnalysisResult, Error>) -> Void) {
        guard let imageData = image.jpegData(compressionQuality: 0.5) else {
            completion(.failure(NSError(domain: "", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to convert image to data"])))
            return
        }
        
        let base64Image = imageData.base64EncodedString()
        
        let parameters: [String: Any] = ["image": base64Image]
        
        // Get API URL and key from Remote Config
        let apiURL = remoteConfig.getString(forKey: "API_URL")
        let apiKey = remoteConfig.getString(forKey: "API_KEY")
        
    
        
        let headers: HTTPHeaders = [
            "Content-Type": "application/json",
            "X-API-Key": apiKey
        ]
        
        AF.request(apiURL, method: .post, parameters: parameters, encoding: JSONEncoding.default, headers: headers)
            .validate()
            .responseDecodable(of: APIHairAnalysisResult.self) { response in
                switch response.result {
                case .success(let result):
                    if let error = result.error {
                        let nsError = NSError(domain: "", code: 0, userInfo: [NSLocalizedDescriptionKey: error])
                        self.logError(nsError)
                        completion(.failure(nsError))
                    } else {
                        completion(.success(result))
                    }
                case .failure(let error):
                    self.logError(error)
                    completion(.failure(error))
                }
            }
    }
    
    private func logError(_ error: Error) {
        let properties: [String: Any] = [
            "error_description": error.localizedDescription,
            "error_code": (error as NSError).code
        ]
        AnalyticsInfoLogger.shared.logEvent("API_Error", properties: properties)
    }
}
