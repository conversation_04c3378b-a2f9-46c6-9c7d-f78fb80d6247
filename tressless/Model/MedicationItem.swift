//
//  MedicationItem.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import Foundation

struct MedicationItem: Identifiable, Codable {
    let id: String // Change to String
        let name: String
        let time: Date // Time of day to take medication
        let repeatInterval: RepeatInterval
        let dosage: String
        let date: Date // Date the medication should be taken


    enum RepeatInterval: String, CaseIterable, Codable, RawRepresentable {
        case daily, weekly, monthly, noReminder
    }
}


