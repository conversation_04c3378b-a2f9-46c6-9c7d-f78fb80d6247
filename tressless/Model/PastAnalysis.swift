//
//  PastAnalysis.swift
//  tressless
//
//  Created by <PERSON> on 26.09.2024.
//
import SwiftUI
import FirebaseFirestore
import FirebaseFirestoreSwift

struct PastAnalysis: Identifiable, Codable {
    var id: Date { date }  // Use the date as the identifier
    var date: Date
    var hairDensity: Double
    var hairLossLevel: String
    var norwoodLevel: String
    var frontalHairCount: Double
    var occipitalHairCount: Double
    var vertexHairCount: Double

    enum CodingKeys: String, CodingKey {
        case date = "timestamp"
        case hairDensity = "finalHairDensity"
        case hairLossLevel
        case norwoodLevel
        case hairCountsPerArea
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        if let timestamp = try container.decodeIfPresent(Timestamp.self, forKey: .date) {
            date = timestamp.dateValue()
        } else {
            date = Date()
        }
        
        hairDensity = try container.decode(Double.self, forKey: .hairDensity)
        hairLossLevel = try container.decode(String.self, forKey: .hairLossLevel)
        norwoodLevel = try container.decode(String.self, forKey: .norwoodLevel)

        let hairCountsPerArea = try container.decode([String: Double].self, forKey: .hairCountsPerArea)
        frontalHairCount = hairCountsPerArea["Frontal scalp"] ?? 0.0
        occipitalHairCount = hairCountsPerArea["Occipital scalp"] ?? 0.0
        vertexHairCount = hairCountsPerArea["Vertex"] ?? 0.0
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(Timestamp(date: date), forKey: .date)
        try container.encode(hairDensity, forKey: .hairDensity)
        try container.encode(hairLossLevel, forKey: .hairLossLevel)
        try container.encode(norwoodLevel, forKey: .norwoodLevel)
        
        let hairCountsPerArea: [String: Double] = [
            "Frontal scalp": frontalHairCount,
            "Occipital scalp": occipitalHairCount,
            "Vertex": vertexHairCount
        ]
        try container.encode(hairCountsPerArea, forKey: .hairCountsPerArea)
    }
}
