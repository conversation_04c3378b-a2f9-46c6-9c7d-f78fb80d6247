//
//  Course.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI

struct Course: Identifiable {
    var id = UUID()
    var title: String
    var subtitle: String
    var caption: String
    var color: Color
    var image: Image
}

var courses = [
    Course(title: "Hair Density Check", subtitle: "Check your hair density with AI", caption: "20 sections - 3 hours", color: Color(hex: "7850F0"), image: Image("Topic 1")),
    Course(title: "Hair Treatment Progress Check", subtitle: "Check your treatment progress level", caption: "47 sections - 11 hours", color: Color(hex: "6792FF"), image: Image("Topic 2")),
    Course(title: "Norwood Hairloss Level", subtitle: "Do you know which level your hairloss is ?", caption: "21 sections - 4 hours", color: Color(hex: "005FE7"), image: Image("Topic 1")),
    Course(title: "Hair Loss Prediction", subtitle: "Solve this test and see if you will lose your hair", caption: "21 sections - 4 hours", color: Color(hex: "005FE7"), image: Image("Topic 1"))
]


