//
//  CourseSection.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import SwiftUI

struct CourseSection: Identifiable {
    var id = UUID()
    var title: String
    var caption: String
    var color: Color
    var image: Image
}

var courseSections = [
    CourseSection(title: "Norwood Level", caption: "Check your Norwood hair loss level with AI", color: Color(hex: "9CC5FF"), image: Image("Topic 2")),
    CourseSection(title: "Density Check", caption: "Check your hair density with AI", color: Color(hex: "6E6AE8"), image: Image("Topic 1")),
    CourseSection(title: "Treatment Progress", caption: "Check your treatment progress level", color: Color(hex: "005FE7"), image: Image("Topic 2")),
    CourseSection(title: "Hair Loss Prediction", caption: "Solve this test and see if you will lose your hair", color: Color(hex: "BBA6FF"), image: Image("Topic 1"))
]
