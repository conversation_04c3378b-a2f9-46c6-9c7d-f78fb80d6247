//
//  BlogPost.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import Foundation

struct BlogPost: Identifiable {
    let id = UUID()
    let title: String
    let brief: String
    let slug: String
    let coverImageUrl: String?
    let coverImageAttribution: String?
    let coverImagePhotographer: String?
    let content: String
}

struct BlogResponse: Decodable {
    let data: UserData
}

struct UserData: Decodable {
    let user: User
}

struct User: Decodable {
    let publications: Publications
}

struct Publications: Decodable {
    let edges: [PublicationEdge]
}

struct PublicationEdge: Decodable {
    let node: PublicationNode
}

struct PublicationNode: Decodable {
    let posts: Posts
}

struct Posts: Decodable {
    let edges: [PostEdge]
}

struct PostEdge: Decodable {
    let node: PostNode
}

struct PostNode: Decodable {
    let title: String
    let brief: String
    let slug: String
    let coverImage: CoverImage?
    let content: Content
}

struct CoverImage: Decodable {
    let url: String?
    let attribution: String?
    let photographer: String?
}

struct Content: Decodable {
    let markdown: String
}
