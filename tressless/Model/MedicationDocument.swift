//
//  MedicationDocument.swift
//  tressless
//
//  Created by <PERSON> on 7.08.2024.
//

import Foundation
import SwiftUI

struct MedicationDocument: Identifiable,  Codable {
    let id: String
    let name: String
    let time: Date
    let repeatInterval: MedicationItem.RepeatInterval
    let dosage: String
    let dates: [Date]
    
    init(id: String, name: String, time: Date, repeatInterval: MedicationItem.RepeatInterval, dosage: String, dates: [Date]) {
        self.id = id
        self.name = name
        self.time = time
        self.repeatInterval = repeatInterval
        self.dosage = dosage
        self.dates = dates
    }
}

