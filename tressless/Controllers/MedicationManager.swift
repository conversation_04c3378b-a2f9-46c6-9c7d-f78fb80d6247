//
//  MedicationManager.swift
//  tressless
//
//  Created by <PERSON> on 6.08.2024.
//

import Foundation
import FirebaseAuth
import FirebaseFirestore

class MedicationManager2: ObservableObject {
    @Published var medications: [MedicationDocument] = []

    func loadMedications() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        let medicationsRef = db.collection("users").document(userId).collection("medications")

        medicationsRef.getDocuments { (querySnapshot, error) in
            if let error = error {
                print("Error getting documents: \(error)")
            } else {
                self.medications = querySnapshot?.documents.compactMap { document in
                    let data = document.data()
                    let id = document.documentID
                    let name = data["name"] as? String ?? ""
                    let time = (data["time"] as? Timestamp)?.dateValue() ?? Date()
                    let repeatInterval = MedicationItem.RepeatInterval(rawValue: data["repeatInterval"] as? String ?? "") ?? .daily
                    let dosage = data["dosage"] as? String ?? ""
                    let dates = (data["dates"] as? [Timestamp])?.compactMap { $0.dateValue() } ?? []

                    return MedicationDocument(id: id, name: name, time: time, repeatInterval: repeatInterval, dosage: dosage, dates: dates)
                } ?? []
            }
        }
    }
    func updateMedications(_ newMedications: [MedicationDocument]) {
           medications = newMedications
       }
    
    func deletemedication(_ medication: MedicationDocument) {
          guard let userId = Auth.auth().currentUser?.uid else { return }
          let medicationRef = db.collection("users").document(userId).collection("medications").document(medication.id)

          medicationRef.delete() { error in
              if let error = error {
                  print("Error deleting medication: \(error)")
              } else {
                  self.medications.removeAll(where: { $0.id == medication.id })
              }
          }
      }
}
