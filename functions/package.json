{"name": "tressless-notifications", "description": "Firebase Functions for Regrow Hair AI push notifications", "scripts": {"lint": "echo 'Skipping lint'", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"firebase-admin": "^12.7.0", "firebase-functions": "^6.0.1"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}