/**
 * Firebase Functions for Push Notifications - Regrow Hair AI
 * 6 Core Notification Types Only
 */

const {onDocumentCreated, onDocumentUpdated, onDocumentDeleted} = require("firebase-functions/v2/firestore");
const {onSchedule} = require("firebase-functions/v2/scheduler");
const {initializeApp} = require("firebase-admin/app");
const {getFirestore, Timestamp, FieldValue} = require("firebase-admin/firestore");
const {getMessaging} = require("firebase-admin/messaging");

initializeApp();
const db = getFirestore();
const messaging = getMessaging();

// ====================================
// 1. SUBSCRIPTION REMINDERS (1 & 3 DAYS)
// ====================================

/**
 * SINGLE SUBSCRIPTION REMINDER FUNCTION
 * Handles ALL subscription reminder scenarios:
 * 1. New users (with or without subscription documents)
 * 2. Free subscription users
 * 3. Users without subscription documents (treated as free)
 */
exports.scheduleSubscriptionReminders = onDocumentCreated("users/{userId}", async (event) => {
  const userId = event.params.userId;
  const userData = event.data.data();

  console.log(`� New user created: ${userId}`);

  try {
    // Check if user has FCM token first
    if (!userData.fcmToken) {
      console.log(`⚠️ User ${userId} has no FCM token, skipping subscription reminders`);
      return;
    }

    // Wait a moment for subscription document to be created (if it will be)
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check subscription status
    const subscriptionDoc = await db.collection("users").doc(userId)
        .collection("subscriptions").doc("current_subscription").get();

    let shouldScheduleReminders = false;

    if (!subscriptionDoc.exists) {
      // No subscription document = treat as free user
      console.log(`💰 User ${userId} has no subscription document, treating as FREE user`);
      shouldScheduleReminders = true;
    } else {
      const subscriptionData = subscriptionDoc.data();
      if (subscriptionData.subscriptionType === "free") {
        console.log(`💰 User ${userId} has FREE subscription`);
        shouldScheduleReminders = true;
      } else {
        console.log(`⏭️ User ${userId} has ${subscriptionData.subscriptionType} subscription, skipping reminders`);
        shouldScheduleReminders = false;
      }
    }

    if (shouldScheduleReminders) {
      const now = new Date();
      const oneDayLater = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      const threeDaysLater = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);

      console.log(`📅 Scheduling subscription reminders for user ${userId}`);

      // Schedule Day 1 reminder
      await db.collection("scheduledNotifications").add({
        userId: userId,
        type: "subscription_reminder_day1",
        scheduledFor: Timestamp.fromDate(oneDayLater),
        status: "pending",
        createdAt: FieldValue.serverTimestamp(),
        title: "Unlock Your Hair Potential! 🚀",
        body: "Ready to supercharge your hair journey? Premium features are waiting for you!",
      });

      // Schedule Day 3 reminder
      await db.collection("scheduledNotifications").add({
        userId: userId,
        type: "subscription_reminder_day3",
        scheduledFor: Timestamp.fromDate(threeDaysLater),
        status: "pending",
        createdAt: FieldValue.serverTimestamp(),
        title: "Limited Time: Premium Features Await! ⏰",
        body: "Don't miss out! Unlock advanced hair analysis and personalized insights today.",
      });

      console.log(`✅ Subscription reminders scheduled for user: ${userId}`);
    }
  } catch (error) {
    console.error(`❌ Error scheduling subscription reminders for user ${userId}:`, error);
  }
});

/**
 * Cancel subscription reminders when user upgrades to premium
 */
exports.cancelSubscriptionRemindersOnUpgrade = onDocumentUpdated("users/{userId}/subscriptions/{subscriptionId}", async (event) => {
  const userId = event.params.userId;
  const beforeData = event.data.before.data();
  const afterData = event.data.after.data();

  // Check if user upgraded from free to premium
  if (beforeData.subscriptionType === "free" && afterData.subscriptionType !== "free") {
    console.log(`🎉 User ${userId} upgraded to ${afterData.subscriptionType}, cancelling subscription reminders`);

    try {
      const pendingReminders = await db.collection("scheduledNotifications")
          .where("userId", "==", userId)
          .where("type", "in", ["subscription_reminder_day1", "subscription_reminder_day3"])
          .where("status", "==", "pending")
          .get();

      if (!pendingReminders.empty) {
        const batch = db.batch();
        pendingReminders.docs.forEach((doc) => {
          batch.update(doc.ref, {
            status: "cancelled",
            cancelReason: "user_upgraded_to_premium",
            cancelledAt: FieldValue.serverTimestamp(),
          });
        });
        await batch.commit();
        console.log(`✅ Cancelled ${pendingReminders.size} pending subscription reminders`);
      }
    } catch (error) {
      console.error(`❌ Error cancelling subscription reminders:`, error);
    }
  }
});

// ====================================
// 2. MEDICATION REMINDERS - EVENT-DRIVEN APPROACH
// ====================================

/**
 * SIMPLE APPROACH: Don't schedule all dates at once
 * Just store medication info and let daily scheduler handle it
 */
exports.onMedicationCreated = onDocumentCreated("users/{userId}/medications/{medicationId}", async (event) => {
  const userId = event.params.userId;
  const medicationId = event.params.medicationId;
  const medicationData = event.data.data();

  console.log(`💊 New medication created: ${medicationData.name} (User: ${userId})`);

  try {
    // Get user's FCM token
    const userDoc = await db.collection("users").doc(userId).get();
    if (!userDoc.exists || !userDoc.data().fcmToken) {
      console.log(`⚠️ User ${userId} has no FCM token, medication noted but no scheduling`);
      return;
    }

    console.log(`✅ Medication ${medicationData.name} ready for daily scheduling`);
  } catch (error) {
    console.error(`❌ Error processing new medication ${medicationId}:`, error);
  }
});

/**
 * SIMPLE APPROACH: Just clean up old notifications when medication is updated
 * Daily scheduler will handle new scheduling
 */
exports.onMedicationUpdated = onDocumentUpdated("users/{userId}/medications/{medicationId}", async (event) => {
  const userId = event.params.userId;
  const medicationId = event.params.medicationId;
  const afterData = event.data.after.data();

  console.log(`🔄 Medication updated: ${afterData.name} (User: ${userId})`);

  try {
    // Clean up old pending notifications for this medication
    const existingNotifications = await db.collection("scheduledNotifications")
        .where("userId", "==", userId)
        .where("medicationId", "==", medicationId)
        .where("status", "==", "pending")
        .get();

    if (!existingNotifications.empty) {
      const batch = db.batch();
      existingNotifications.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      await batch.commit();
      console.log(`🗑️ Cleaned up ${existingNotifications.size} old notifications`);
    }

    console.log(`✅ Medication ${afterData.name} updated, daily scheduler will handle new notifications`);
  } catch (error) {
    console.error(`❌ Error updating medication ${medicationId}:`, error);
  }
});



/**
 * Handle medication deletion - clean up any pending notifications
 */
exports.onMedicationDeleted = onDocumentDeleted("users/{userId}/medications/{medicationId}", async (event) => {
  const userId = event.params.userId;
  const medicationId = event.params.medicationId;

  console.log(`🗑️ Cleaning up notifications for deleted medication: ${medicationId}`);

  try {
    const notifications = await db.collection("scheduledNotifications")
        .where("userId", "==", userId)
        .where("medicationId", "==", medicationId)
        .where("status", "==", "pending")
        .get();

    if (!notifications.empty) {
      const batch = db.batch();
      notifications.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      await batch.commit();

      console.log(`✅ Deleted ${notifications.size} pending notifications for medication ${medicationId}`);
    } else {
      console.log(`ℹ️ No pending notifications found for medication ${medicationId}`);
    }
  } catch (error) {
    console.error(`❌ Error deleting notifications for medication ${medicationId}:`, error);
  }
});

// ====================================
// 3. DAILY MEDICATION SCHEDULER - SIMPLE & EFFICIENT
// ====================================

/**
 * SIMPLE DAILY SCHEDULER: Schedule tomorrow's medication notifications
 * Runs once daily at 6 PM, only schedules next day notifications
 * Much more efficient than scheduling 365 notifications at once
 */
exports.scheduleTomorrowMedications = onSchedule("0 18 * * *", async (event) => {
  console.log("💊 Daily medication scheduler starting...");

  try {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0); // Start of tomorrow

    const dayAfterTomorrow = new Date(tomorrow);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 1); // End of tomorrow

    console.log(`📅 Scheduling medications for: ${tomorrow.toDateString()}`);

    let totalScheduled = 0;
    let usersProcessed = 0;
    let lastDoc = null;
    let hasMore = true;

    // Process users in batches to avoid timeout
    while (hasMore) {
      let query = db.collection("users")
          .where("fcmToken", "!=", "")
          .limit(100); // Smaller batches for reliability

      if (lastDoc) {
        query = query.startAfter(lastDoc);
      }

      const usersWithTokens = await query.get();

      if (usersWithTokens.empty) {
        hasMore = false;
        break;
      }

      console.log(`👥 Processing batch of ${usersWithTokens.size} users`);

      // Process each user in this batch
      for (const userDoc of usersWithTokens.docs) {
      const userId = userDoc.id;
      const userData = userDoc.data();

      try {
        // Get user's medications
        const medicationsSnapshot = await db.collection("users").doc(userId)
            .collection("medications")
            .get();

        if (medicationsSnapshot.empty) {
          continue; // Skip users with no medications
        }

        let userScheduledCount = 0;

        // Check each medication
        for (const medicationDoc of medicationsSnapshot.docs) {
          const medication = medicationDoc.data();
          const medicationId = medicationDoc.id;

          // Check if this medication should be taken tomorrow
          const dates = medication.dates.map(date => date.toDate());
          const shouldTakeTomorrow = dates.some(date => {
            const medicationDate = new Date(date);
            medicationDate.setHours(0, 0, 0, 0);
            return medicationDate.getTime() === tomorrow.getTime();
          });

          if (shouldTakeTomorrow) {
            // Check if notification already exists for tomorrow
            const existingNotification = await db.collection("scheduledNotifications")
                .where("userId", "==", userId)
                .where("medicationId", "==", medicationId)
                .where("scheduledDate", ">=", Timestamp.fromDate(tomorrow))
                .where("scheduledDate", "<", Timestamp.fromDate(dayAfterTomorrow))
                .where("status", "==", "pending")
                .limit(1)
                .get();

            if (existingNotification.empty) {
              // Schedule notification for tomorrow
              const medicationTime = medication.time.toDate();
              const notificationTime = new Date(tomorrow);
              notificationTime.setHours(medicationTime.getHours(), medicationTime.getMinutes(), 0, 0);

              await db.collection("scheduledNotifications").add({
                userId: userId,
                type: "daily_routine_reminder",
                medicationId: medicationId,
                medicationName: medication.name,
                scheduledFor: Timestamp.fromDate(notificationTime),
                status: "pending",
                createdAt: FieldValue.serverTimestamp(),
                scheduledDate: Timestamp.fromDate(tomorrow),
              });

              userScheduledCount++;
              totalScheduled++;
            }
          }
        }

        if (userScheduledCount > 0) {
          console.log(`📋 User ${userId}: scheduled ${userScheduledCount} notifications`);
        }

        usersProcessed++;

      } catch (userError) {
        console.error(`❌ Error processing user ${userId}:`, userError);
      }
    }

      // Update pagination
      lastDoc = usersWithTokens.docs[usersWithTokens.docs.length - 1];

      // If we got less than the limit, we're done
      if (usersWithTokens.size < 100) {
        hasMore = false;
      }

      console.log(`📊 Batch complete: ${usersProcessed} users processed so far`);
    }

    console.log(`✅ Daily scheduling complete:`);
    console.log(`  👥 Users processed: ${usersProcessed}`);
    console.log(`  💊 Notifications scheduled: ${totalScheduled}`);

  } catch (error) {
    console.error("❌ Error in daily medication scheduler:", error);
  }
});

// ====================================
// 4. MILESTONE CELEBRATIONS
// ====================================

/**
 * Celebrate hair analysis milestones
 */
exports.celebrateAnalysisMilestone = onDocumentCreated("users/{userId}/hair_density_analyses/{analysisId}", async (event) => {
  const userId = event.params.userId;

  try {
    // OPTIMIZATION: Use aggregation query to count instead of reading all documents
    const analysesCountQuery = await db.collection("users").doc(userId)
        .collection("hair_density_analyses")
        .count()
        .get();
    const analysisCount = analysesCountQuery.data().count;

    console.log(`User ${userId} completed analysis #${analysisCount}`);

    let milestone = null;

    if (analysisCount === 1) {
      milestone = {
        title: "🎉 First Analysis Complete!",
        body: "Amazing start! You've taken the first step in your hair restoration journey.",
      };
    } else if (analysisCount === 5) {
      milestone = {
        title: "🏆 5-Analysis Milestone!",
        body: "Incredible dedication! You're building strong habits for tracking your hair health.",
      };
    } else if (analysisCount === 10) {
      milestone = {
        title: "⭐ 10-Analysis Champion!",
        body: "Outstanding commitment! You're becoming a true hair health expert.",
      };
    } else if (analysisCount === 25) {
      milestone = {
        title: "👑 25-Analysis Master!",
        body: "Phenomenal consistency! You're a true hair transformation champion!",
      };
    }

    if (milestone) {
      // Get user's FCM token
      const userDoc = await db.collection("users").doc(userId).get();
      if (userDoc.exists && userDoc.data().fcmToken) {
        await sendNotification(userId, userDoc.data().fcmToken, {
          ...milestone,
          type: "milestone_celebration",
          analysisCount: analysisCount,
        });

        console.log(`✅ Sent milestone celebration to user: ${userId} (${analysisCount} analyses)`);
      } else {
        console.log(`⚠️ User ${userId} has no FCM token, skipping milestone notification`);
      }
    }
  } catch (error) {
    console.error(`❌ Error celebrating milestone for user ${userId}:`, error);
  }
});

// Milestone celebrations are handled by analysis milestones only

// ====================================
// 5. SMART INACTIVITY TRIGGERS - EVENT-DRIVEN
// ====================================

/**
 * Track user activity when they perform actions
 * This replaces expensive daily scanning with efficient event tracking
 */
exports.trackUserActivity = onDocumentCreated("users/{userId}/hair_density_analyses/{analysisId}", async (event) => {
  const userId = event.params.userId;

  try {
    // Update user's last activity timestamp
    await db.collection("users").doc(userId).update({
      lastActivityAt: FieldValue.serverTimestamp(),
      lastActivityType: "hair_analysis"
    });

    // Cancel any pending inactivity notifications for this user
    const pendingInactivityNotifications = await db.collection("scheduledNotifications")
        .where("userId", "==", userId)
        .where("type", "in", ["inactivity_3_days", "inactivity_1_week"])
        .where("status", "==", "pending")
        .get();

    if (!pendingInactivityNotifications.empty) {
      const batch = db.batch();
      pendingInactivityNotifications.docs.forEach((doc) => {
        batch.update(doc.ref, { status: "cancelled", cancelReason: "user_became_active" });
      });
      await batch.commit();
      console.log(`🔄 Cancelled ${pendingInactivityNotifications.size} inactivity notifications for active user ${userId}`);
    }

    console.log(`📊 Updated activity tracking for user: ${userId}`);
  } catch (error) {
    console.error(`❌ Error tracking activity for user ${userId}:`, error);
  }
});

/**
 * Schedule inactivity reminders when user becomes inactive
 * Runs weekly instead of daily to reduce costs
 */
exports.scheduleInactivityReminders = onSchedule("0 18 * * 0", async (event) => {
  console.log("🔍 Weekly inactivity check - scheduling reminders for inactive users...");

  try {
    const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    // Find users who haven't been active recently
    const inactiveUsersSnapshot = await db.collection("users")
        .where("fcmToken", "!=", "")
        .where("lastActivityAt", "<", Timestamp.fromDate(threeDaysAgo))
        .limit(200) // Process more users since we run weekly
        .get();

    console.log(`📋 Found ${inactiveUsersSnapshot.size} potentially inactive users`);

    let scheduled3Day = 0;
    let scheduled1Week = 0;

    for (const userDoc of inactiveUsersSnapshot.docs) {
      const userId = userDoc.id;
      const userData = userDoc.data();
      const lastActivity = userData.lastActivityAt ? userData.lastActivityAt.toDate() : userData.createdAt?.toDate();

      if (!lastActivity) continue;

      const daysSinceActivity = Math.floor((Date.now() - lastActivity.getTime()) / (24 * 60 * 60 * 1000));

      // Check if we already have pending notifications for this user
      const existingNotifications = await db.collection("scheduledNotifications")
          .where("userId", "==", userId)
          .where("type", "in", ["inactivity_3_days", "inactivity_1_week"])
          .where("status", "==", "pending")
          .get();

      if (!existingNotifications.empty) {
        console.log(`⏭️ User ${userId} already has pending inactivity notifications`);
        continue;
      }

      let notificationType = null;
      let title = "";
      let body = "";
      let scheduleFor = new Date();

      if (daysSinceActivity >= 7) {
        // Schedule 1-week inactivity reminder
        notificationType = "inactivity_1_week";
        title = "Don't Give Up! 💪";
        body = "Great hair takes time and consistency. Your transformation journey is waiting for you!";
        scheduleFor = new Date(Date.now() + 24 * 60 * 60 * 1000); // Tomorrow
        scheduled1Week++;
      } else if (daysSinceActivity >= 3) {
        // Schedule 3-day inactivity reminder
        notificationType = "inactivity_3_days";
        title = "We Miss You! 😊";
        body = "Your hair journey is waiting! Come back and continue tracking your amazing progress.";
        scheduleFor = new Date(Date.now() + 24 * 60 * 60 * 1000); // Tomorrow
        scheduled3Day++;
      }

      if (notificationType) {
        await db.collection("scheduledNotifications").add({
          userId: userId,
          type: notificationType,
          title: title,
          body: body,
          scheduledFor: Timestamp.fromDate(scheduleFor),
          status: "pending",
          createdAt: FieldValue.serverTimestamp(),
          daysSinceActivity: daysSinceActivity
        });

        console.log(`📅 Scheduled ${notificationType} for user ${userId} (${daysSinceActivity} days inactive)`);
      }
    }

    console.log(`🎯 Inactivity scheduling complete:`);
    console.log(`  📱 3-day reminders scheduled: ${scheduled3Day}`);
    console.log(`  📱 1-week reminders scheduled: ${scheduled1Week}`);

  } catch (error) {
    console.error("❌ Error in scheduleInactivityReminders:", error);
  }
});

// ====================================
// 6. SMART MOTIVATIONAL BOOSTS - TARGETED APPROACH
// ====================================

/**
 * Send personalized motivational messages based on user engagement
 * Runs monthly instead of weekly to reduce costs and increase impact
 */
exports.sendPersonalizedMotivationalBoosts = onSchedule("0 10 1 * *", async (event) => {
  console.log("📅 Monthly personalized motivational boost campaign...");

  try {
    const oneMonthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    // Get active users (those who have been active in the last month)
    const activeUsersSnapshot = await db.collection("users")
        .where("fcmToken", "!=", "")
        .where("lastActivityAt", ">", Timestamp.fromDate(oneMonthAgo))
        .limit(500) // Process more users since we run monthly
        .get();

    console.log(`👥 Found ${activeUsersSnapshot.size} active users for motivational boosts`);

    let highEngagementCount = 0;
    let mediumEngagementCount = 0;
    let lowEngagementCount = 0;

    for (const userDoc of activeUsersSnapshot.docs) {
      const userId = userDoc.id;
      const userData = userDoc.data();

      // Get user's analysis count in the last month
      const recentAnalysesCount = await db.collection("users").doc(userId)
          .collection("hair_density_analyses")
          .where("timestamp", ">", Timestamp.fromDate(oneMonthAgo))
          .count()
          .get();

      const analysisCount = recentAnalysesCount.data().count;
      let message = null;

      // Personalize message based on engagement level
      if (analysisCount >= 4) {
        // High engagement - 4+ analyses in a month
        message = {
          title: "Amazing Consistency! 🏆",
          body: `${analysisCount} analyses this month! Your dedication to tracking progress is truly inspiring. Keep it up!`,
          type: "motivational_boost_high_engagement"
        };
        highEngagementCount++;
      } else if (analysisCount >= 2) {
        // Medium engagement - 2-3 analyses
        message = {
          title: "Great Progress! 📈",
          body: `${analysisCount} analyses this month shows real commitment. Every step counts in your hair journey!`,
          type: "motivational_boost_medium_engagement"
        };
        mediumEngagementCount++;
      } else if (analysisCount >= 1) {
        // Low engagement - 1 analysis
        message = {
          title: "Keep Going! 💪",
          body: "You're on the right track! Regular monitoring is key to successful hair transformation.",
          type: "motivational_boost_low_engagement"
        };
        lowEngagementCount++;
      }

      if (message) {
        // Schedule the notification for a random time in the next 7 days
        const randomDelay = Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000); // 0-7 days
        const scheduleFor = new Date(Date.now() + randomDelay);

        await db.collection("scheduledNotifications").add({
          userId: userId,
          type: message.type,
          title: message.title,
          body: message.body,
          scheduledFor: Timestamp.fromDate(scheduleFor),
          status: "pending",
          createdAt: FieldValue.serverTimestamp(),
          analysisCount: analysisCount
        });

        console.log(`📅 Scheduled personalized boost for user ${userId} (${analysisCount} analyses)`);
      }
    }

    console.log(`🎯 Motivational boost scheduling complete:`);
    console.log(`  🏆 High engagement: ${highEngagementCount}`);
    console.log(`  📈 Medium engagement: ${mediumEngagementCount}`);
    console.log(`  💪 Low engagement: ${lowEngagementCount}`);

  } catch (error) {
    console.error("❌ Error in sendPersonalizedMotivationalBoosts:", error);
  }
});

// ====================================
// 7. REACTIVATION AFTER SUBSCRIPTION CANCELLATION
// ====================================

/**
 * Handle subscription cancellation and schedule reactivation campaigns
 */
exports.scheduleReactivationCampaign = onDocumentUpdated("users/{userId}/subscriptions/current_subscription", async (event) => {
  const userId = event.params.userId;
  const beforeData = event.data.before.data();
  const afterData = event.data.after.data();

  try {
    // Check if subscription was cancelled (premium -> free)
    if (beforeData?.subscriptionType === "premium" && afterData?.subscriptionType === "free") {
      console.log(`Subscription cancelled for user: ${userId} - scheduling reactivation campaign`);

      // Get user's FCM token for immediate acknowledgment
      const userDoc = await db.collection("users").doc(userId).get();
      if (userDoc.exists && userDoc.data().fcmToken) {
        await sendNotification(userId, userDoc.data().fcmToken, {
          title: "Thank You for Trying Premium! 💙",
          body: "Your progress data is safely stored. We'll be here when you're ready to continue your journey.",
          type: "subscription_cancelled",
        });
      } else {
        console.log(`⚠️ User ${userId} has no FCM token, skipping cancellation acknowledgment`);
      }

      // Schedule reactivation campaign
      const reactivationSchedule = [
        {
          days: 3,
          title: "Missing Premium Features? 🎯",
          body: "Come back and continue your hair transformation with unlimited analyses and advanced insights!",
        },
        {
          days: 7,
          title: "Your Hair Goals Are Still Achievable! 💪",
          body: "Restart your premium journey today and unlock the full power of AI-driven hair analysis.",
        },
        {
          days: 14,
          title: "Special Offer Just for You! 🎁",
          body: "Exclusive 20% discount on premium features. Resume your hair transformation journey today!",
        },
        {
          days: 30,
          title: "We Miss You! 😊",
          body: "Your hair transformation is waiting. Come back and see how much you can achieve with premium features.",
        },
      ];

      for (const campaign of reactivationSchedule) {
        await db.collection("scheduledNotifications").add({
          userId: userId,
          type: "reactivation_campaign",
          scheduledFor: Timestamp.fromDate(
              new Date(Date.now() + campaign.days * 24 * 60 * 60 * 1000),
          ),
          title: campaign.title,
          body: campaign.body,
          status: "pending",
          createdAt: FieldValue.serverTimestamp(),
        });
      }

      console.log(`✅ Reactivation campaign scheduled for user: ${userId}`);
    }
  } catch (error) {
    console.error(`❌ Error handling subscription change for user ${userId}:`, error);
  }
});

// ====================================
// SMART NOTIFICATION PROCESSOR - HOURLY WITH BATCHING
// ====================================

/**
 * Process scheduled notifications hourly (much more efficient than every 15 minutes)
 * Only processes notifications that are actually due
 */
exports.processScheduledNotifications = onSchedule("0 * * * *", async (event) => {
  const startTime = new Date();
  console.log(`🔄 Starting hourly notification processor at ${startTime.toISOString()}`);

  try {
    const now = Timestamp.now();
    const oneHourFromNow = Timestamp.fromDate(new Date(Date.now() + 60 * 60 * 1000));

    // Get notifications due in the next hour (more efficient batching)
    const dueNotifications = await db.collection("scheduledNotifications")
        .where("status", "==", "pending")
        .where("scheduledFor", "<=", oneHourFromNow)
        .limit(200) // Increased batch size since we run less frequently
        .get();

    console.log(`📋 Found ${dueNotifications.size} notifications due in next hour`);

    if (dueNotifications.empty) {
      console.log("✨ No notifications to process");
      return;
    }

    // Group notifications by scheduled time for efficient processing
    const notificationGroups = new Map();

    for (const doc of dueNotifications.docs) {
      const notification = doc.data();
      const scheduledTime = notification.scheduledFor.toDate().getTime();

      if (!notificationGroups.has(scheduledTime)) {
        notificationGroups.set(scheduledTime, []);
      }
      notificationGroups.get(scheduledTime).push({ id: doc.id, data: notification });
    }

    let totalSent = 0;
    let totalSkipped = 0;
    let totalFailed = 0;

    // Process each time group
    for (const [scheduledTime, notifications] of notificationGroups) {
      const scheduleDate = new Date(scheduledTime);

      // Only process if the scheduled time has passed
      if (scheduleDate <= new Date()) {
        console.log(`⏰ Processing ${notifications.length} notifications for ${scheduleDate.toISOString()}`);

        const results = await processNotificationBatch(notifications);
        totalSent += results.sent;
        totalSkipped += results.skipped;
        totalFailed += results.failed;
      }
    }

    const endTime = new Date();
    const processingTime = endTime.getTime() - startTime.getTime();

    console.log(`🎯 Hourly processing complete:`);
    console.log(`  ✅ Sent: ${totalSent}`);
    console.log(`  ⏭️ Skipped: ${totalSkipped}`);
    console.log(`  ❌ Failed: ${totalFailed}`);
    console.log(`  ⏱️ Processing time: ${processingTime}ms`);

  } catch (error) {
    console.error("❌ Critical error in processScheduledNotifications:", error);
  }
});

/**
 * Process a batch of notifications efficiently
 */
async function processNotificationBatch(notifications) {
  let sent = 0;
  let skipped = 0;
  let failed = 0;

  // Process notifications in parallel for better performance
  const promises = notifications.map(async ({ id, data: notification }) => {
    try {
      let fcmToken = null;

      // Handle anonymous notifications
      if (notification.userId === "anonymous") {
        fcmToken = notification.fcmToken;
        if (!fcmToken) {
          await db.collection("scheduledNotifications").doc(id).update({
            status: "failed",
            failureReason: "no_anonymous_fcm_token",
            processedAt: FieldValue.serverTimestamp(),
          });
          return { result: "failed" };
        }
      } else {
        // Handle regular user notifications
        const userDoc = await db.collection("users").doc(notification.userId).get();
        if (!userDoc.exists || !userDoc.data().fcmToken) {
          await db.collection("scheduledNotifications").doc(id).update({
            status: "failed",
            failureReason: "user_not_found_or_no_token",
            processedAt: FieldValue.serverTimestamp(),
          });
          return { result: "failed" };
        }
        fcmToken = userDoc.data().fcmToken;

        // Check subscription status for subscription-related notifications
        if (notification.type.includes("subscription_reminder")) {
          const subscriptionDoc = await db.collection("users").doc(notification.userId)
              .collection("subscriptions").doc("current_subscription").get();

          if (subscriptionDoc.exists) {
            const subscriptionData = subscriptionDoc.data();
            if (subscriptionData.subscriptionType === "premium") {
              console.log(`⏭️ User ${notification.userId} has premium subscription, skipping subscription reminder`);
              await db.collection("scheduledNotifications").doc(id).update({
                status: "skipped",
                skipReason: "user_has_premium_subscription",
                processedAt: FieldValue.serverTimestamp(),
              });
              return { result: "skipped" };
            }
          }
          // If no subscription document exists, treat as free user and send reminder
          console.log(`💰 User ${notification.userId} has no subscription document, treating as FREE user - sending reminder`);
        }
      }

      // Special handling for medication reminders (only for authenticated users)
      if (notification.type === "daily_routine_reminder" && notification.userId !== "anonymous") {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const completionCount = await db.collection("users").doc(notification.userId)
            .collection("medication_completions")
            .where("medicationId", "==", notification.medicationId)
            .where("date", ">=", Timestamp.fromDate(today))
            .where("isCompleted", "==", true)
            .count()
            .get();

        if (completionCount.data().count > 0) {
          await db.collection("scheduledNotifications").doc(id).update({
            status: "skipped",
            skipReason: "already_completed",
            processedAt: FieldValue.serverTimestamp(),
          });
          return { result: "skipped" };
        }

        // Send medication reminder
        const success = await sendNotification(notification.userId, fcmToken, {
          title: `${notification.medicationName} 💊`,
          body: "Time for your medication! Tap to mark as complete.",
          type: notification.type,
          medicationId: notification.medicationId,
        });

        if (success) {
          await db.collection("scheduledNotifications").doc(id).update({
            status: "sent",
            sentAt: FieldValue.serverTimestamp(),
            processedAt: FieldValue.serverTimestamp(),
          });
          return { result: "sent" };
        }
      } else {
        // Send other notification types (including anonymous notifications)
        const success = await sendNotification(notification.userId, fcmToken, {
          title: notification.title || getNotificationTitle(notification.type),
          body: notification.body || getNotificationBody(notification.type),
          type: notification.type,
        });

        if (success) {
          await db.collection("scheduledNotifications").doc(id).update({
            status: "sent",
            sentAt: FieldValue.serverTimestamp(),
            processedAt: FieldValue.serverTimestamp(),
          });
          return { result: "sent" };
        }
      }

      return { result: "failed" };
    } catch (error) {
      console.error(`❌ Error processing notification ${id}:`, error);
      await db.collection("scheduledNotifications").doc(id).update({
        status: "failed",
        failureReason: error.message || "unknown_error",
        processedAt: FieldValue.serverTimestamp(),
      });
      return { result: "failed" };
    }
  });

  const results = await Promise.all(promises);

  results.forEach(({ result }) => {
    if (result === "sent") sent++;
    else if (result === "skipped") skipped++;
    else if (result === "failed") failed++;
  });

  return { sent, skipped, failed };
}

// ====================================
// OPTIMIZED ANONYMOUS TOKEN HANDLING
// ====================================

/**
 * Schedule anonymous subscription reminders when tokens are created
 * This replaces daily scanning with efficient event-driven approach
 */
exports.scheduleAnonymousSubscriptionReminders = onDocumentCreated("anonymousTokens/{tokenId}", async (event) => {
  const tokenData = event.data.data();
  const fcmToken = tokenData.fcmToken;

  console.log(`📱 Scheduling subscription reminders for new anonymous token: ${fcmToken.substring(0, 10)}...`);

  try {
    const now = new Date();
    const oneDayLater = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const threeDaysLater = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);

    // Schedule Day 1 reminder
    await db.collection("scheduledNotifications").add({
      userId: "anonymous",
      fcmToken: fcmToken,
      type: "subscription_reminder_day1",
      scheduledFor: Timestamp.fromDate(oneDayLater),
      status: "pending",
      createdAt: FieldValue.serverTimestamp(),
      title: "Unlock Your Hair Potential! 🚀",
      body: "Ready to supercharge your hair journey? Premium features are waiting for you!",
    });

    // Schedule Day 3 reminder
    await db.collection("scheduledNotifications").add({
      userId: "anonymous",
      fcmToken: fcmToken,
      type: "subscription_reminder_day3",
      scheduledFor: Timestamp.fromDate(threeDaysLater),
      status: "pending",
      createdAt: FieldValue.serverTimestamp(),
      title: "Limited Time: Premium Features Await! ⏰",
      body: "Don't miss out! Unlock advanced hair analysis and personalized insights today.",
    });

    console.log(`✅ Scheduled subscription reminders for anonymous token`);
  } catch (error) {
    console.error(`❌ Error scheduling anonymous subscription reminders:`, error);
  }
});

/**
 * Clean up anonymous notifications when token is migrated to user account
 */
exports.cleanupAnonymousNotifications = onDocumentDeleted("anonymousTokens/{tokenId}", async (event) => {
  const encodedTokenId = event.params.tokenId;
  // Decode the token ID back to original FCM token (replace _ with :)
  const originalFcmToken = encodedTokenId.replace(/_/g, ":");

  console.log(`🧹 Cleaning up notifications for migrated anonymous token: ${encodedTokenId}`);

  try {
    // Cancel pending notifications for this anonymous token
    const pendingNotifications = await db.collection("scheduledNotifications")
        .where("fcmToken", "==", originalFcmToken)
        .where("userId", "==", "anonymous")
        .where("status", "==", "pending")
        .get();

    if (!pendingNotifications.empty) {
      const batch = db.batch();
      pendingNotifications.docs.forEach((doc) => {
        batch.update(doc.ref, {
          status: "cancelled",
          cancelReason: "token_migrated_to_user_account"
        });
      });
      await batch.commit();

      console.log(`✅ Cancelled ${pendingNotifications.size} pending notifications for migrated token`);
    }
  } catch (error) {
    console.error(`❌ Error cleaning up anonymous notifications:`, error);
  }
});

/**
 * Clean up old anonymous tokens - runs weekly instead of daily
 * Much more efficient approach
 */
exports.cleanupOldAnonymousTokens = onSchedule("0 3 * * 0", async (event) => {
  console.log("🧹 Weekly cleanup of old anonymous tokens...");

  try {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // Process in batches to avoid timeout
    let totalCleaned = 0;
    let hasMore = true;

    while (hasMore) {
      const oldTokens = await db.collection("anonymousTokens")
          .where("createdAt", "<", Timestamp.fromDate(thirtyDaysAgo))
          .limit(100) // Smaller batches for reliability
          .get();

      if (oldTokens.empty) {
        hasMore = false;
        break;
      }

      const batch = db.batch();
      oldTokens.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      totalCleaned += oldTokens.size;

      console.log(`🗑️ Cleaned up batch of ${oldTokens.size} old tokens (total: ${totalCleaned})`);

      // If we got less than the limit, we're done
      if (oldTokens.size < 100) {
        hasMore = false;
      }
    }

    console.log(`✅ Weekly cleanup complete: ${totalCleaned} old anonymous tokens removed`);

  } catch (error) {
    console.error("❌ Error in cleanupOldAnonymousTokens:", error);
  }
});

// ====================================
// HELPER FUNCTIONS
// ====================================



/**
 * Enhanced notification sending with comprehensive logging
 */
async function sendNotification(userId, fcmToken, notificationData) {
  console.log(`📤 Attempting to send notification to user ${userId}:`);
  console.log(`  Token: ${fcmToken.substring(0, 20)}...`);
  console.log(`  Title: ${notificationData.title}`);
  console.log(`  Type: ${notificationData.type}`);

  try {
    const message = {
      token: fcmToken,
      notification: {
        title: notificationData.title,
        body: notificationData.body,
      },
      data: {
        type: notificationData.type,
        userId: userId,
        ...(notificationData.medicationId && {medicationId: notificationData.medicationId}),
      },
      apns: {
        payload: {
          aps: {
            sound: "default",
            badge: 1,
          },
        },
      },
      android: {
        notification: {
          sound: "default",
          clickAction: "FLUTTER_NOTIFICATION_CLICK",
        },
      },
    };

    console.log(`🚀 Sending FCM message...`);
    const response = await messaging.send(message);

    console.log(`✅ FCM message sent successfully:`);
    console.log(`  Message ID: ${response}`);

    // Log successful notification
    await logNotification(userId, notificationData.type, "sent", {
      messageId: response,
      title: notificationData.title,
      fcmToken: fcmToken.substring(0, 20) + "...",
    });

    return true;
  } catch (error) {
    console.error(`❌ Failed to send FCM message to user ${userId}:`, error);
    console.error(`  Error code: ${error.code}`);
    console.error(`  Error message: ${error.message}`);

    // Log failed notification
    await logNotification(userId, notificationData.type, "failed", {
      error: error.message,
      errorCode: error.code,
      fcmToken: fcmToken.substring(0, 20) + "...",
    });

    return false;
  }
}

/**
 * Log notification attempts for debugging
 */
async function logNotification(userId, type, status, details) {
  try {
    await db.collection("notificationLogs").add({
      userId: userId,
      type: type,
      status: status,
      details: details,
      timestamp: FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error("Failed to log notification:", error);
  }
}

/**
 * Get default notification titles
 */
function getNotificationTitle(type) {
  const titles = {
    subscription_reminder_day1: "Unlock Your Hair Potential! 🚀",
    subscription_reminder_day3: "Limited Time: Premium Features Await! ⏰",
    milestone_celebration: "Congratulations! 🎉",
    inactivity_3_days: "We Miss You! 😊",
    inactivity_1_week: "Don't Give Up! 💪",
    motivational_boost: "Keep Growing! 🌱",
    reactivation_campaign: "Come Back to Premium! 🎯",
    subscription_cancelled: "Thank You! 💙",
  };
  return titles[type] || "Hair Journey Update 📱";
}

/**
 * Get default notification bodies
 */
function getNotificationBody(type) {
  const bodies = {
    subscription_reminder_day1: "Ready to supercharge your hair journey? Premium features are waiting for you!",
    subscription_reminder_day3: "Don't miss out! Unlock advanced hair analysis and personalized insights today.",
    milestone_celebration: "Amazing progress on your hair transformation journey!",
    inactivity_3_days: "Your hair journey is waiting! Come back and continue tracking your amazing progress.",
    inactivity_1_week: "Great hair takes time and consistency. Your transformation journey is waiting for you!",
    motivational_boost: "Every day of care brings you closer to your hair goals. Your consistency is paying off!",
    reactivation_campaign: "Restart your premium journey and unlock the full power of AI-driven hair analysis.",
    subscription_cancelled: "Your progress data is safely stored. We'll be here when you're ready to continue.",
  };
  return bodies[type] || "Check your hair journey progress in the app!";
}

















