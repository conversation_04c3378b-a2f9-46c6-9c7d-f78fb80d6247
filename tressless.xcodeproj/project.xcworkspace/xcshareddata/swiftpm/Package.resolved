{"originHash": "2973bdd95b5c8243fdc6dc5fe5de696923c62f1c76614d59b0dcec261bd6cbc5", "pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "194a6706acbd25e4ef639bcaddea16e8758a3e27", "version": "1.2024011602.0"}}, {"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire.git", "state": {"revision": "f455c2975872ccd2d9c81594c658af65716e9b9a", "version": "5.9.1"}}, {"identity": "alamofireimage", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/AlamofireImage.git", "state": {"revision": "1eaf3b6c6882bed10f6e7b119665599dd2329aa1", "version": "4.3.0"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "3b62f154d00019ae29a71e9738800bb6f18b236d", "version": "10.19.2"}}, {"identity": "appauth-ios", "kind": "remoteSourceControl", "location": "https://github.com/openid/AppAuth-iOS.git", "state": {"revision": "c89ed571ae140f8eb1142735e6e23d7bb8c34cb2", "version": "1.7.5"}}, {"identity": "contentful.swift", "kind": "remoteSourceControl", "location": "https://github.com/contentful/contentful.swift/", "state": {"revision": "9662dccfe515bcc0c0aa5517663accea3f6209e1", "version": "5.5.11"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "state": {"revision": "eca84fd638116dd6adb633b5a3f31cc7befcbb7d", "version": "10.29.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "fe727587518729046fc1465625b9afd80b5ab361", "version": "10.28.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "a637d318ae7ae246b02d7305121275bc75ed5565", "version": "9.4.0"}}, {"identity": "googlesignin-ios", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleSignIn-iOS", "state": {"revision": "a7965d134c5d3567026c523e0a8a583f73b62b0d", "version": "7.1.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "57a1d307f42df690fdef2637f3e5b776da02aad6", "version": "7.13.3"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "e9fad491d0673bdda7063a0341fb6b47a30c5359", "version": "1.62.2"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "gtmappauth", "kind": "remoteSourceControl", "location": "https://github.com/google/GTMAppAuth.git", "state": {"revision": "5d7d66f647400952b1758b230e019b07c0b4b22a", "version": "4.1.1"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "2d12673670417654f08f5f90fdd62926dc3a2648", "version": "100.0.0"}}, {"identity": "kingfisher", "kind": "remoteSourceControl", "location": "https://github.com/onevcat/Kingfisher.git", "state": {"revision": "2ef543ee21d63734e1c004ad6c870255e8716c50", "version": "7.12.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "lottie-ios", "kind": "remoteSourceControl", "location": "https://github.com/airbnb/lottie-ios", "state": {"revision": "fe4c6fe3a0aa66cdeb51d549623c82ca9704b9a5", "version": "4.5.0"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "networkimage", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/NetworkImage", "state": {"revision": "7aff8d1b31148d32c5933d75557d42f6323ee3d1", "version": "6.0.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "purchases-ios", "kind": "remoteSourceControl", "location": "https://github.com/RevenueCat/purchases-ios.git", "state": {"revision": "e90dec89a46199cc57eb53d1523ff8606edac276", "version": "5.30.0"}}, {"identity": "rich-text-renderer.swift", "kind": "remoteSourceControl", "location": "https://github.com/contentful/rich-text-renderer.swift", "state": {"revision": "9fbc69c2244d37460bdd4e2aa8352dda05c70bd0", "version": "0.4.2"}}, {"identity": "rive-ios", "kind": "remoteSourceControl", "location": "https://github.com/rive-app/rive-ios", "state": {"revision": "432053b91f458c7f1359adea69ef3850e74c1402", "version": "2.0.20"}}, {"identity": "sdwebimage", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImage.git", "state": {"revision": "86e9185ef41c4238a93ad8efe61ddeb701e80bbf", "version": "5.19.5"}}, {"identity": "sdwebimageswiftui", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImageSwiftUI", "state": {"revision": "5d462f7530677ae0c2b9510c26383aa25ba48751", "version": "3.1.1"}}, {"identity": "swift-markdown-ui", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/swift-markdown-ui", "state": {"revision": "55441810c0f678c78ed7e2ebd46dde89228e02fc", "version": "2.4.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "e17d61f26df0f0e06f58f6977ba05a097a720106", "version": "1.27.1"}}, {"identity": "swiftfulfirebaseauth", "kind": "remoteSourceControl", "location": "https://github.com/SwiftfulThinking/SwiftfulFirebaseAuth.git", "state": {"revision": "1c981615e3436c6ed7fd1a0bd3be16300a05863a", "version": "1.0.6"}}], "version": 3}