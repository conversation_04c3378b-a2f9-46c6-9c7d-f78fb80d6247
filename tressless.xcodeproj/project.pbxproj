// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 63;
	objects = {

/* Begin PBXBuildFile section */
		4187755A2DE8144C0017380D /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 418775592DE8144C0017380D /* NotificationService.swift */; };
		65081D4F2DFC2CA4001256DE /* FirstNoticeChangesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65081D4E2DFC2CA2001256DE /* FirstNoticeChangesView.swift */; };
		65081D512DFC2EA5001256DE /* FeelHairSituationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65081D502DFC2EA4001256DE /* FeelHairSituationView.swift */; };
		6552D0AD2DC116B9004CDC0F /* ScalpConditionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6552D0AC2DC116B3004CDC0F /* ScalpConditionView.swift */; };
		6552D0AF2DC1190D004CDC0F /* FamilyHistoryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6552D0AE2DC11907004CDC0F /* FamilyHistoryView.swift */; };
		65F0B6F92DDBB5BF0046BC6B /* ProgressGraphView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65F0B6F82DDBB5BB0046BC6B /* ProgressGraphView.swift */; };
		CB0FFE7A2C9193B200A1F243 /* icons.riv in Resources */ = {isa = PBXBuildFile; fileRef = CB0FFE792C9193B200A1F243 /* icons.riv */; };
		CB0FFE802C91DE3300A1F243 /* RemoteConfigManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB0FFE7F2C91DE3300A1F243 /* RemoteConfigManager.swift */; };
		CB0FFE822C91DE5100A1F243 /* FirebaseRemoteConfig in Frameworks */ = {isa = PBXBuildFile; productRef = CB0FFE812C91DE5100A1F243 /* FirebaseRemoteConfig */; };
		CB0FFE842C95170300A1F243 /* AppAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB0FFE832C95170300A1F243 /* AppAlert.swift */; };
		CB0FFE862C9568DA00A1F243 /* AsButtonViewModifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB0FFE852C9568DA00A1F243 /* AsButtonViewModifier.swift */; };
		CB0FFE882C95697000A1F243 /* TabbarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB0FFE872C95697000A1F243 /* TabbarView.swift */; };
		CB0FFE8A2C95699C00A1F243 /* RootView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB0FFE892C95699C00A1F243 /* RootView.swift */; };
		CB0FFE8C2C9569C000A1F243 /* CreateAccountView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB0FFE8B2C9569C000A1F243 /* CreateAccountView.swift */; };
		CB0FFEAE2C9570BC00A1F243 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = CB0FFEAD2C9570BC00A1F243 /* Assets.xcassets */; };
		CB1A3C3E2C986F33002DB3C6 /* DeepLinkHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB1A3C3D2C986F33002DB3C6 /* DeepLinkHandler.swift */; };
		CB2B12262CBDCA0300250578 /* follicle.json in Resources */ = {isa = PBXBuildFile; fileRef = CB2B12252CBDCA0300250578 /* follicle.json */; };
		CB2B12282CBDCA4200250578 /* LottieView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB2B12272CBDCA4200250578 /* LottieView.swift */; };
		CB7DA78E2C637260000952A5 /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = CB7DA78D2C637260000952A5 /* Alamofire */; };
		************************ /* GIFBackgroundView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* GIFBackgroundView.swift */; };
		************************ /* PastAnalysesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* PastAnalysesView.swift */; };
		************************ /* PastAnalysis.swift in Sources */ = {isa = PBXBuildFile; fileRef = CB98843D2CA5E26900B6950C /* PastAnalysis.swift */; };
		CB9884402CA5E7B800B6950C /* FirebaseFirestoreSwift in Frameworks */ = {isa = PBXBuildFile; productRef = CB98843F2CA5E7B800B6950C /* FirebaseFirestoreSwift */; };
		CB99E0452C62DE6700FC8F16 /* RiveRuntime in Frameworks */ = {isa = PBXBuildFile; productRef = CB99E0442C62DE6700FC8F16 /* RiveRuntime */; };
		CBAD51E42CBDCB8000B1BF13 /* Lottie in Frameworks */ = {isa = PBXBuildFile; productRef = CBAD51E32CBDCB8000B1BF13 /* Lottie */; };
		CBAD523D2CBDEBA500B1BF13 /* scanning.json in Resources */ = {isa = PBXBuildFile; fileRef = CBAD523C2CBDEBA500B1BF13 /* scanning.json */; };
		CBAD523F2CBDF22000B1BF13 /* scan.json in Resources */ = {isa = PBXBuildFile; fileRef = CBAD523E2CBDF22000B1BF13 /* scan.json */; };
		CBD1FFCF2D402A67008D5602 /* LockedResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD1FFCE2D402A67008D5602 /* LockedResultsView.swift */; };
		CBD74E662C62C4A70040A146 /* tresslessApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74E652C62C4A70040A146 /* tresslessApp.swift */; };
		CBD74E6D2C62C4A90040A146 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = CBD74E6C2C62C4A90040A146 /* Preview Assets.xcassets */; };
		CBD74E742C62C4E70040A146 /* ImageService.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74E732C62C4E70040A146 /* ImageService.swift */; };
		CBD74E782C62C53C0040A146 /* String.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74E772C62C53C0040A146 /* String.swift */; };
		CBD74E7B2C62C58B0040A146 /* MedicationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74E7A2C62C58B0040A146 /* MedicationManager.swift */; };
		CBD74E7D2C62C5A50040A146 /* LocalFileManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74E7C2C62C5A50040A146 /* LocalFileManager.swift */; };
		CBD74E862C62C6A70040A146 /* TabBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74E852C62C6A70040A146 /* TabBar.swift */; };
		CBD74E8C2C62C7510040A146 /* BlogHome.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74E8B2C62C7510040A146 /* BlogHome.swift */; };
		CBD74E9D2C62C99B0040A146 /* HomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74E9C2C62C99B0040A146 /* HomeView.swift */; };
		CBD74E9F2C62C9BC0040A146 /* OnboardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74E9E2C62C9BC0040A146 /* OnboardView.swift */; };
		CBD74EA12C62C9F20040A146 /* ProgressView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EA02C62C9F20040A146 /* ProgressView.swift */; };
		CBD74EA32C62CA690040A146 /* GeminiView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EA22C62CA690040A146 /* GeminiView.swift */; };
		CBD74EA52C62CA8E0040A146 /* ReminderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EA42C62CA8E0040A146 /* ReminderView.swift */; };
		CBD74EA72C62CC140040A146 /* PathView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EA62C62CC140040A146 /* PathView.swift */; };
		CBD74EA92C62CD370040A146 /* AddReminderView2.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EA82C62CD370040A146 /* AddReminderView2.swift */; };
		CBD74EAB2C62CE2E0040A146 /* ClassifyView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EAA2C62CE2E0040A146 /* ClassifyView.swift */; };
		CBD74EAD2C62CE480040A146 /* ImageGuideView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EAC2C62CE480040A146 /* ImageGuideView.swift */; };
		CBD74EAF2C62CE650040A146 /* ResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EAE2C62CE650040A146 /* ResultsView.swift */; };
		CBD74EB12C62CE8B0040A146 /* ImagePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EB02C62CE8B0040A146 /* ImagePicker.swift */; };
		CBD74EB42C62CEBC0040A146 /* HCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EB32C62CEBC0040A146 /* HCard.swift */; };
		CBD74EB62C62CEDC0040A146 /* VCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EB52C62CEDC0040A146 /* VCard.swift */; };
		CBD74EB92C62CF280040A146 /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EB82C62CF280040A146 /* Extensions.swift */; };
		CBD74EBD2C62CF630040A146 /* CustomButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EBC2C62CF630040A146 /* CustomButton.swift */; };
		CBD74EC12C62CFB20040A146 /* Colors.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EC02C62CFB20040A146 /* Colors.swift */; };
		CBD74EC42C62CFFA0040A146 /* Course.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EC32C62CFFA0040A146 /* Course.swift */; };
		CBD74EC62C62D00F0040A146 /* CourseSection.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EC52C62D00F0040A146 /* CourseSection.swift */; };
		CBD74EC82C62D0210040A146 /* MedicationItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EC72C62D0210040A146 /* MedicationItem.swift */; };
		CBD74ECA2C62D0320040A146 /* MedicationDocument.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EC92C62D0320040A146 /* MedicationDocument.swift */; };
		CBD74ECC2C62D0440040A146 /* BlogPost.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74ECB2C62D0440040A146 /* BlogPost.swift */; };
		CBD74ED02C62D07D0040A146 /* Classify.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74ECF2C62D07D0040A146 /* Classify.swift */; };
		CBD74ED92C62D0A20040A146 /* button.riv in Resources */ = {isa = PBXBuildFile; fileRef = CBD74ED12C62D0A20040A146 /* button.riv */; };
		CBD74EDA2C62D0A20040A146 /* check.riv in Resources */ = {isa = PBXBuildFile; fileRef = CBD74ED22C62D0A20040A146 /* check.riv */; };
		CBD74EDC2C62D0A20040A146 /* house.riv in Resources */ = {isa = PBXBuildFile; fileRef = CBD74ED42C62D0A20040A146 /* house.riv */; };
		CBD74EDD2C62D0A20040A146 /* icons2.riv in Resources */ = {isa = PBXBuildFile; fileRef = CBD74ED52C62D0A20040A146 /* icons2.riv */; };
		CBD74EDE2C62D0A20040A146 /* menu_button.riv in Resources */ = {isa = PBXBuildFile; fileRef = CBD74ED62C62D0A20040A146 /* menu_button.riv */; };
		CBD74EDF2C62D0A20040A146 /* shapes.riv in Resources */ = {isa = PBXBuildFile; fileRef = CBD74ED72C62D0A20040A146 /* shapes.riv */; };
		CBD74EE52C62D0AD0040A146 /* Inter-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CBD74EE02C62D0AD0040A146 /* Inter-Regular.ttf */; };
		CBD74EE62C62D0AD0040A146 /* Inter-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CBD74EE12C62D0AD0040A146 /* Inter-SemiBold.ttf */; };
		CBD74EE72C62D0AD0040A146 /* KdamThmorPro-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CBD74EE22C62D0AD0040A146 /* KdamThmorPro-Regular.ttf */; };
		CBD74EE82C62D0AD0040A146 /* Poppins-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CBD74EE32C62D0AD0040A146 /* Poppins-Bold.ttf */; };
		CBD74EEF2C62D26E0040A146 /* Contentful in Frameworks */ = {isa = PBXBuildFile; productRef = CBD74EEE2C62D26E0040A146 /* Contentful */; };
		CBD74EF22C62D27E0040A146 /* Kingfisher in Frameworks */ = {isa = PBXBuildFile; productRef = CBD74EF12C62D27E0040A146 /* Kingfisher */; };
		CBD74EF52C62D29C0040A146 /* SDWebImageSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = CBD74EF42C62D29C0040A146 /* SDWebImageSwiftUI */; };
		CBD74EF82C62D4460040A146 /* FirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = CBD74EF72C62D4460040A146 /* FirebaseAuth */; };
		CBD74EFA2C62D4460040A146 /* FirebaseFirestore in Frameworks */ = {isa = PBXBuildFile; productRef = CBD74EF92C62D4460040A146 /* FirebaseFirestore */; };
		CBD74EFC2C62D4460040A146 /* FirebaseStorage in Frameworks */ = {isa = PBXBuildFile; productRef = CBD74EFB2C62D4460040A146 /* FirebaseStorage */; };
		CBD74EFE2C62D4FF0040A146 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBD74EFD2C62D4FF0040A146 /* ContentView.swift */; };
		CBDBEA7B2C6A99EC008EF17F /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = CBDBEA7A2C6A99EC008EF17F /* GoogleSignIn */; };
		CBDBEA7D2C6A99EC008EF17F /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = CBDBEA7C2C6A99EC008EF17F /* GoogleSignInSwift */; };
		CBF373452C6ACBDE00A348C2 /* SwiftfulFirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = CBF373442C6ACBDE00A348C2 /* SwiftfulFirebaseAuth */; };
		CBF373532C6ACF5600A348C2 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = CBF373522C6ACF5600A348C2 /* GoogleService-Info.plist */; };
		CBF373562C6BF70200A348C2 /* RevenueCat in Frameworks */ = {isa = PBXBuildFile; productRef = CBF373552C6BF70200A348C2 /* RevenueCat */; };
		CBF373582C6BF70200A348C2 /* RevenueCatUI in Frameworks */ = {isa = PBXBuildFile; productRef = CBF373572C6BF70200A348C2 /* RevenueCatUI */; };
		CBF3735A2C6C070800A348C2 /* Tressless.storekit in Resources */ = {isa = PBXBuildFile; fileRef = CBF373592C6C070800A348C2 /* Tressless.storekit */; };
		CBF373612C6C153400A348C2 /* PayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBF373602C6C153400A348C2 /* PayView.swift */; };
		CBF373712C71313100A348C2 /* SelectionButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBF373702C71313100A348C2 /* SelectionButton.swift */; };
		CBF373742C7135A100A348C2 /* ReviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBF373732C7135A100A348C2 /* ReviewView.swift */; };
		CBF3737B2C75274A00A348C2 /* AgeSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBF3737A2C75274A00A348C2 /* AgeSelectionView.swift */; };
		CBF3737D2C75292600A348C2 /* GoalsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBF3737C2C75292600A348C2 /* GoalsView.swift */; };
		CBF373812C75465600A348C2 /* ToolsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBF373802C75465600A348C2 /* ToolsView.swift */; };
		CBF8EB822C62D9DB00DD96DA /* ContentfulRichTextRenderer in Frameworks */ = {isa = PBXBuildFile; productRef = CBF8EB812C62D9DB00DD96DA /* ContentfulRichTextRenderer */; };
		CBFB0FFD2C7DDFC2009F18B4 /* BlogService.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBFB0FFC2C7DDFC2009F18B4 /* BlogService.swift */; };
		CBFB10002C7DE4D0009F18B4 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = CBFB0FFF2C7DE4D0009F18B4 /* MarkdownUI */; };
		CBFB10032C7E816B009F18B4 /* UserGuidingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBFB10022C7E816B009F18B4 /* UserGuidingView.swift */; };
		CBFB100D2C807B8F009F18B4 /* ProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBFB100C2C807B8F009F18B4 /* ProfileView.swift */; };
		CBFB101C2C81BD34009F18B4 /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = CBFB101B2C81BD34009F18B4 /* FirebaseAnalytics */; };
		CBFB101E2C81BD34009F18B4 /* FirebaseMessaging in Frameworks */ = {isa = PBXBuildFile; productRef = CBFB101D2C81BD34009F18B4 /* FirebaseMessaging */; };
		CBFB10202C84FDCE009F18B4 /* WelcomeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBFB101F2C84FDCE009F18B4 /* WelcomeView.swift */; };
		CBFB10262C865B47009F18B4 /* CustomAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBFB10252C865B47009F18B4 /* CustomAlertView.swift */; };
		CBFB10282C86660B009F18B4 /* CustomizingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBFB10272C86660B009F18B4 /* CustomizingView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		418775592DE8144C0017380D /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		65081D4E2DFC2CA2001256DE /* FirstNoticeChangesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirstNoticeChangesView.swift; sourceTree = "<group>"; };
		65081D502DFC2EA4001256DE /* FeelHairSituationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FeelHairSituationView.swift; sourceTree = "<group>"; };
		6552D0AC2DC116B3004CDC0F /* ScalpConditionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScalpConditionView.swift; sourceTree = "<group>"; };
		6552D0AE2DC11907004CDC0F /* FamilyHistoryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FamilyHistoryView.swift; sourceTree = "<group>"; };
		65F0B6F82DDBB5BB0046BC6B /* ProgressGraphView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProgressGraphView.swift; sourceTree = "<group>"; };
		CB0FFE792C9193B200A1F243 /* icons.riv */ = {isa = PBXFileReference; lastKnownFileType = file; path = icons.riv; sourceTree = "<group>"; };
		CB0FFE7F2C91DE3300A1F243 /* RemoteConfigManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemoteConfigManager.swift; sourceTree = "<group>"; };
		CB0FFE832C95170300A1F243 /* AppAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppAlert.swift; sourceTree = "<group>"; };
		CB0FFE852C9568DA00A1F243 /* AsButtonViewModifier.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AsButtonViewModifier.swift; sourceTree = "<group>"; };
		CB0FFE872C95697000A1F243 /* TabbarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabbarView.swift; sourceTree = "<group>"; };
		CB0FFE892C95699C00A1F243 /* RootView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RootView.swift; sourceTree = "<group>"; };
		CB0FFE8B2C9569C000A1F243 /* CreateAccountView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateAccountView.swift; sourceTree = "<group>"; };
		CB0FFEAD2C9570BC00A1F243 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		CB1A3C3D2C986F33002DB3C6 /* DeepLinkHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeepLinkHandler.swift; sourceTree = "<group>"; };
		CB2B12252CBDCA0300250578 /* follicle.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = follicle.json; sourceTree = "<group>"; };
		CB2B12272CBDCA4200250578 /* LottieView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LottieView.swift; sourceTree = "<group>"; };
		************************ /* GIFBackgroundView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GIFBackgroundView.swift; sourceTree = "<group>"; };
		************************ /* PastAnalysesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PastAnalysesView.swift; sourceTree = "<group>"; };
		CB98843D2CA5E26900B6950C /* PastAnalysis.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PastAnalysis.swift; sourceTree = "<group>"; };
		CBAD523C2CBDEBA500B1BF13 /* scanning.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = scanning.json; sourceTree = "<group>"; };
		CBAD523E2CBDF22000B1BF13 /* scan.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = scan.json; sourceTree = "<group>"; };
		CBD1FFCE2D402A67008D5602 /* LockedResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LockedResultsView.swift; sourceTree = "<group>"; };
		CBD74E622C62C4A70040A146 /* tressless.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = tressless.app; sourceTree = BUILT_PRODUCTS_DIR; };
		CBD74E652C62C4A70040A146 /* tresslessApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = tresslessApp.swift; sourceTree = "<group>"; };
		CBD74E6C2C62C4A90040A146 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		CBD74E732C62C4E70040A146 /* ImageService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageService.swift; sourceTree = "<group>"; };
		CBD74E772C62C53C0040A146 /* String.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = String.swift; sourceTree = "<group>"; };
		CBD74E7A2C62C58B0040A146 /* MedicationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MedicationManager.swift; sourceTree = "<group>"; };
		CBD74E7C2C62C5A50040A146 /* LocalFileManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocalFileManager.swift; sourceTree = "<group>"; };
		CBD74E852C62C6A70040A146 /* TabBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabBar.swift; sourceTree = "<group>"; };
		CBD74E8B2C62C7510040A146 /* BlogHome.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BlogHome.swift; sourceTree = "<group>"; };
		CBD74E9C2C62C99B0040A146 /* HomeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeView.swift; sourceTree = "<group>"; };
		CBD74E9E2C62C9BC0040A146 /* OnboardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OnboardView.swift; sourceTree = "<group>"; };
		CBD74EA02C62C9F20040A146 /* ProgressView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProgressView.swift; sourceTree = "<group>"; };
		CBD74EA22C62CA690040A146 /* GeminiView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeminiView.swift; sourceTree = "<group>"; };
		CBD74EA42C62CA8E0040A146 /* ReminderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReminderView.swift; sourceTree = "<group>"; };
		CBD74EA62C62CC140040A146 /* PathView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PathView.swift; sourceTree = "<group>"; };
		CBD74EA82C62CD370040A146 /* AddReminderView2.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddReminderView2.swift; sourceTree = "<group>"; };
		CBD74EAA2C62CE2E0040A146 /* ClassifyView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClassifyView.swift; sourceTree = "<group>"; };
		CBD74EAC2C62CE480040A146 /* ImageGuideView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageGuideView.swift; sourceTree = "<group>"; };
		CBD74EAE2C62CE650040A146 /* ResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultsView.swift; sourceTree = "<group>"; };
		CBD74EB02C62CE8B0040A146 /* ImagePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePicker.swift; sourceTree = "<group>"; };
		CBD74EB32C62CEBC0040A146 /* HCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HCard.swift; sourceTree = "<group>"; };
		CBD74EB52C62CEDC0040A146 /* VCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VCard.swift; sourceTree = "<group>"; };
		CBD74EB82C62CF280040A146 /* Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		CBD74EBC2C62CF630040A146 /* CustomButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomButton.swift; sourceTree = "<group>"; };
		CBD74EC02C62CFB20040A146 /* Colors.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Colors.swift; sourceTree = "<group>"; };
		CBD74EC32C62CFFA0040A146 /* Course.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Course.swift; sourceTree = "<group>"; };
		CBD74EC52C62D00F0040A146 /* CourseSection.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CourseSection.swift; sourceTree = "<group>"; };
		CBD74EC72C62D0210040A146 /* MedicationItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MedicationItem.swift; sourceTree = "<group>"; };
		CBD74EC92C62D0320040A146 /* MedicationDocument.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MedicationDocument.swift; sourceTree = "<group>"; };
		CBD74ECB2C62D0440040A146 /* BlogPost.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BlogPost.swift; sourceTree = "<group>"; };
		CBD74ECF2C62D07D0040A146 /* Classify.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Classify.swift; sourceTree = "<group>"; };
		CBD74ED12C62D0A20040A146 /* button.riv */ = {isa = PBXFileReference; lastKnownFileType = file; path = button.riv; sourceTree = "<group>"; };
		CBD74ED22C62D0A20040A146 /* check.riv */ = {isa = PBXFileReference; lastKnownFileType = file; path = check.riv; sourceTree = "<group>"; };
		CBD74ED42C62D0A20040A146 /* house.riv */ = {isa = PBXFileReference; lastKnownFileType = file; path = house.riv; sourceTree = "<group>"; };
		CBD74ED52C62D0A20040A146 /* icons2.riv */ = {isa = PBXFileReference; lastKnownFileType = file; path = icons2.riv; sourceTree = "<group>"; };
		CBD74ED62C62D0A20040A146 /* menu_button.riv */ = {isa = PBXFileReference; lastKnownFileType = file; path = menu_button.riv; sourceTree = "<group>"; };
		CBD74ED72C62D0A20040A146 /* shapes.riv */ = {isa = PBXFileReference; lastKnownFileType = file; path = shapes.riv; sourceTree = "<group>"; };
		CBD74EE02C62D0AD0040A146 /* Inter-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Inter-Regular.ttf"; sourceTree = "<group>"; };
		CBD74EE12C62D0AD0040A146 /* Inter-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Inter-SemiBold.ttf"; sourceTree = "<group>"; };
		CBD74EE22C62D0AD0040A146 /* KdamThmorPro-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "KdamThmorPro-Regular.ttf"; sourceTree = "<group>"; };
		CBD74EE32C62D0AD0040A146 /* Poppins-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Bold.ttf"; sourceTree = "<group>"; };
		CBD74EFD2C62D4FF0040A146 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		CBF3732D2C6AA33500A348C2 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		CBF3733F2C6AACC400A348C2 /* tressless.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = tressless.entitlements; sourceTree = "<group>"; };
		CBF373522C6ACF5600A348C2 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		CBF373592C6C070800A348C2 /* Tressless.storekit */ = {isa = PBXFileReference; lastKnownFileType = text; path = Tressless.storekit; sourceTree = "<group>"; };
		CBF373602C6C153400A348C2 /* PayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PayView.swift; sourceTree = "<group>"; };
		CBF373702C71313100A348C2 /* SelectionButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SelectionButton.swift; sourceTree = "<group>"; };
		CBF373732C7135A100A348C2 /* ReviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReviewView.swift; sourceTree = "<group>"; };
		CBF3737A2C75274A00A348C2 /* AgeSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AgeSelectionView.swift; sourceTree = "<group>"; };
		CBF3737C2C75292600A348C2 /* GoalsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoalsView.swift; sourceTree = "<group>"; };
		CBF373802C75465600A348C2 /* ToolsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ToolsView.swift; sourceTree = "<group>"; };
		CBFB0FFC2C7DDFC2009F18B4 /* BlogService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BlogService.swift; sourceTree = "<group>"; };
		CBFB10012C7E7AF4009F18B4 /* feature.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = feature.md; sourceTree = "<group>"; };
		CBFB10022C7E816B009F18B4 /* UserGuidingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserGuidingView.swift; sourceTree = "<group>"; };
		CBFB100C2C807B8F009F18B4 /* ProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileView.swift; sourceTree = "<group>"; };
		CBFB101F2C84FDCE009F18B4 /* WelcomeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WelcomeView.swift; sourceTree = "<group>"; };
		CBFB10252C865B47009F18B4 /* CustomAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomAlertView.swift; sourceTree = "<group>"; };
		CBFB10272C86660B009F18B4 /* CustomizingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomizingView.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		CBD74E5F2C62C4A70040A146 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CBF373562C6BF70200A348C2 /* RevenueCat in Frameworks */,
				CB7DA78E2C637260000952A5 /* Alamofire in Frameworks */,
				CBD74EF52C62D29C0040A146 /* SDWebImageSwiftUI in Frameworks */,
				CBD74EF22C62D27E0040A146 /* Kingfisher in Frameworks */,
				CBF8EB822C62D9DB00DD96DA /* ContentfulRichTextRenderer in Frameworks */,
				CBF373582C6BF70200A348C2 /* RevenueCatUI in Frameworks */,
				CBD74EEF2C62D26E0040A146 /* Contentful in Frameworks */,
				CBDBEA7B2C6A99EC008EF17F /* GoogleSignIn in Frameworks */,
				CB99E0452C62DE6700FC8F16 /* RiveRuntime in Frameworks */,
				CBFB101C2C81BD34009F18B4 /* FirebaseAnalytics in Frameworks */,
				CBAD51E42CBDCB8000B1BF13 /* Lottie in Frameworks */,
				CBF373452C6ACBDE00A348C2 /* SwiftfulFirebaseAuth in Frameworks */,
				CBFB10002C7DE4D0009F18B4 /* MarkdownUI in Frameworks */,
				CBD74EFA2C62D4460040A146 /* FirebaseFirestore in Frameworks */,
				CB0FFE822C91DE5100A1F243 /* FirebaseRemoteConfig in Frameworks */,
				CBD74EF82C62D4460040A146 /* FirebaseAuth in Frameworks */,
				CBD74EFC2C62D4460040A146 /* FirebaseStorage in Frameworks */,
				CBFB101E2C81BD34009F18B4 /* FirebaseMessaging in Frameworks */,
				CBDBEA7D2C6A99EC008EF17F /* GoogleSignInSwift in Frameworks */,
				CB9884402CA5E7B800B6950C /* FirebaseFirestoreSwift in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		CB18ED192C62DD6F00F25C9B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		CBD74E592C62C4A70040A146 = {
			isa = PBXGroup;
			children = (
				CBD74E642C62C4A70040A146 /* tressless */,
				CBD74E632C62C4A70040A146 /* Products */,
				CB18ED192C62DD6F00F25C9B /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		CBD74E632C62C4A70040A146 /* Products */ = {
			isa = PBXGroup;
			children = (
				CBD74E622C62C4A70040A146 /* tressless.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		CBD74E642C62C4A70040A146 /* tressless */ = {
			isa = PBXGroup;
			children = (
				CBF3733F2C6AACC400A348C2 /* tressless.entitlements */,
				CBF3735B2C6C072F00A348C2 /* Paywall */,
				CBF373592C6C070800A348C2 /* Tressless.storekit */,
				CBF373522C6ACF5600A348C2 /* GoogleService-Info.plist */,
				CB0FFEAD2C9570BC00A1F243 /* Assets.xcassets */,
				CBF3732D2C6AA33500A348C2 /* Info.plist */,
				CBD74EC22C62CFDF0040A146 /* Model */,
				CBD74E792C62C5560040A146 /* Controllers */,
				CBD74E762C62C5250040A146 /* String */,
				CBD74E752C62C4EC0040A146 /* Services */,
				CBD74E652C62C4A70040A146 /* tresslessApp.swift */,
				CBD74E822C62C63F0040A146 /* Navigation */,
				CBD74E872C62C7100040A146 /* Views */,
				CBD74EE42C62D0AD0040A146 /* Fonts */,
				CBD74ED82C62D0A20040A146 /* RiveAssets */,
				CBD74EB72C62CF0F0040A146 /* Styles */,
				CBD74EB22C62CEAA0040A146 /* Components */,
				CBD74E6B2C62C4A90040A146 /* Preview Content */,
			);
			path = tressless;
			sourceTree = "<group>";
		};
		CBD74E6B2C62C4A90040A146 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				CBD74E6C2C62C4A90040A146 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		CBD74E752C62C4EC0040A146 /* Services */ = {
			isa = PBXGroup;
			children = (
				418775592DE8144C0017380D /* NotificationService.swift */,
				CBD74E732C62C4E70040A146 /* ImageService.swift */,
				CBFB0FFC2C7DDFC2009F18B4 /* BlogService.swift */,
				CB1A3C3D2C986F33002DB3C6 /* DeepLinkHandler.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		CBD74E762C62C5250040A146 /* String */ = {
			isa = PBXGroup;
			children = (
				CBD74E772C62C53C0040A146 /* String.swift */,
			);
			path = String;
			sourceTree = "<group>";
		};
		CBD74E792C62C5560040A146 /* Controllers */ = {
			isa = PBXGroup;
			children = (
				CBD74E7A2C62C58B0040A146 /* MedicationManager.swift */,
				CBD74E7C2C62C5A50040A146 /* LocalFileManager.swift */,
			);
			path = Controllers;
			sourceTree = "<group>";
		};
		CBD74E822C62C63F0040A146 /* Navigation */ = {
			isa = PBXGroup;
			children = (
				CBD74E852C62C6A70040A146 /* TabBar.swift */,
				CBD74EFD2C62D4FF0040A146 /* ContentView.swift */,
			);
			path = Navigation;
			sourceTree = "<group>";
		};
		CBD74E872C62C7100040A146 /* Views */ = {
			isa = PBXGroup;
			children = (
				CBD74E952C62C8BD0040A146 /* Auth */,
				CBD74E882C62C7180040A146 /* Blog */,
				CBD74E9C2C62C99B0040A146 /* HomeView.swift */,
				************************ /* PastAnalysesView.swift */,
				CBF373802C75465600A348C2 /* ToolsView.swift */,
				CBF373722C71358900A348C2 /* Onboarding */,
				CBD74EA02C62C9F20040A146 /* ProgressView.swift */,
				CBD74EA22C62CA690040A146 /* GeminiView.swift */,
				CBD74EA42C62CA8E0040A146 /* ReminderView.swift */,
				CBD74EA62C62CC140040A146 /* PathView.swift */,
				CB2B12272CBDCA4200250578 /* LottieView.swift */,
				CBD74EA82C62CD370040A146 /* AddReminderView2.swift */,
				CBD1FFCE2D402A67008D5602 /* LockedResultsView.swift */,
				CBD74EAA2C62CE2E0040A146 /* ClassifyView.swift */,
				CB2B12252CBDCA0300250578 /* follicle.json */,
				CBAD523E2CBDF22000B1BF13 /* scan.json */,
				CBAD523C2CBDEBA500B1BF13 /* scanning.json */,
				CB0FFE7F2C91DE3300A1F243 /* RemoteConfigManager.swift */,
				CBFB10252C865B47009F18B4 /* CustomAlertView.swift */,
				CBFB10022C7E816B009F18B4 /* UserGuidingView.swift */,
				CBFB100C2C807B8F009F18B4 /* ProfileView.swift */,
				CBD74EAC2C62CE480040A146 /* ImageGuideView.swift */,
				CBFB10012C7E7AF4009F18B4 /* feature.md */,
				CBD74EAE2C62CE650040A146 /* ResultsView.swift */,
				CBD74EB02C62CE8B0040A146 /* ImagePicker.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		CBD74E882C62C7180040A146 /* Blog */ = {
			isa = PBXGroup;
			children = (
				CBD74E8B2C62C7510040A146 /* BlogHome.swift */,
			);
			path = Blog;
			sourceTree = "<group>";
		};
		CBD74E952C62C8BD0040A146 /* Auth */ = {
			isa = PBXGroup;
			children = (
				CB0FFE832C95170300A1F243 /* AppAlert.swift */,
				CB0FFE852C9568DA00A1F243 /* AsButtonViewModifier.swift */,
				CB0FFE8B2C9569C000A1F243 /* CreateAccountView.swift */,
				CB0FFE872C95697000A1F243 /* TabbarView.swift */,
				CB0FFE892C95699C00A1F243 /* RootView.swift */,
			);
			path = Auth;
			sourceTree = "<group>";
		};
		CBD74EB22C62CEAA0040A146 /* Components */ = {
			isa = PBXGroup;
			children = (
				CBD74EB32C62CEBC0040A146 /* HCard.swift */,
				CBD74EB52C62CEDC0040A146 /* VCard.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		CBD74EB72C62CF0F0040A146 /* Styles */ = {
			isa = PBXGroup;
			children = (
				CBD74EB82C62CF280040A146 /* Extensions.swift */,
				CBF373702C71313100A348C2 /* SelectionButton.swift */,
				CBD74EBC2C62CF630040A146 /* CustomButton.swift */,
				CBD74EC02C62CFB20040A146 /* Colors.swift */,
			);
			path = Styles;
			sourceTree = "<group>";
		};
		CBD74EC22C62CFDF0040A146 /* Model */ = {
			isa = PBXGroup;
			children = (
				CBD74EC32C62CFFA0040A146 /* Course.swift */,
				CBD74EC52C62D00F0040A146 /* CourseSection.swift */,
				CBD74EC72C62D0210040A146 /* MedicationItem.swift */,
				CBD74EC92C62D0320040A146 /* MedicationDocument.swift */,
				CBD74ECB2C62D0440040A146 /* BlogPost.swift */,
				CBD74ECF2C62D07D0040A146 /* Classify.swift */,
				CB98843D2CA5E26900B6950C /* PastAnalysis.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		CBD74ED82C62D0A20040A146 /* RiveAssets */ = {
			isa = PBXGroup;
			children = (
				CBD74ED12C62D0A20040A146 /* button.riv */,
				CBD74ED22C62D0A20040A146 /* check.riv */,
				CBD74ED42C62D0A20040A146 /* house.riv */,
				CB0FFE792C9193B200A1F243 /* icons.riv */,
				CBD74ED52C62D0A20040A146 /* icons2.riv */,
				CBD74ED62C62D0A20040A146 /* menu_button.riv */,
				CBD74ED72C62D0A20040A146 /* shapes.riv */,
			);
			path = RiveAssets;
			sourceTree = "<group>";
		};
		CBD74EE42C62D0AD0040A146 /* Fonts */ = {
			isa = PBXGroup;
			children = (
				CBD74EE02C62D0AD0040A146 /* Inter-Regular.ttf */,
				CBD74EE12C62D0AD0040A146 /* Inter-SemiBold.ttf */,
				CBD74EE22C62D0AD0040A146 /* KdamThmorPro-Regular.ttf */,
				CBD74EE32C62D0AD0040A146 /* Poppins-Bold.ttf */,
			);
			path = Fonts;
			sourceTree = "<group>";
		};
		CBF3735B2C6C072F00A348C2 /* Paywall */ = {
			isa = PBXGroup;
			children = (
				CBF373602C6C153400A348C2 /* PayView.swift */,
			);
			path = Paywall;
			sourceTree = "<group>";
		};
		CBF373722C71358900A348C2 /* Onboarding */ = {
			isa = PBXGroup;
			children = (
				65081D4E2DFC2CA2001256DE /* FirstNoticeChangesView.swift */,
				65081D502DFC2EA4001256DE /* FeelHairSituationView.swift */,
				CBD74E9E2C62C9BC0040A146 /* OnboardView.swift */,
				65F0B6F82DDBB5BB0046BC6B /* ProgressGraphView.swift */,
				************************ /* GIFBackgroundView.swift */,
				CBFB10272C86660B009F18B4 /* CustomizingView.swift */,
				CBF3737A2C75274A00A348C2 /* AgeSelectionView.swift */,
				6552D0AC2DC116B3004CDC0F /* ScalpConditionView.swift */,
				6552D0AE2DC11907004CDC0F /* FamilyHistoryView.swift */,
				CBF373732C7135A100A348C2 /* ReviewView.swift */,
				CBF3737C2C75292600A348C2 /* GoalsView.swift */,
				CBFB101F2C84FDCE009F18B4 /* WelcomeView.swift */,
			);
			path = Onboarding;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		CBD74E612C62C4A70040A146 /* tressless */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CBD74E702C62C4A90040A146 /* Build configuration list for PBXNativeTarget "tressless" */;
			buildPhases = (
				CBD74E5E2C62C4A70040A146 /* Sources */,
				CBD74E5F2C62C4A70040A146 /* Frameworks */,
				CBD74E602C62C4A70040A146 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = tressless;
			packageProductDependencies = (
				CBD74EEE2C62D26E0040A146 /* Contentful */,
				CBD74EF12C62D27E0040A146 /* Kingfisher */,
				CBD74EF42C62D29C0040A146 /* SDWebImageSwiftUI */,
				CBD74EF72C62D4460040A146 /* FirebaseAuth */,
				CBD74EF92C62D4460040A146 /* FirebaseFirestore */,
				CBD74EFB2C62D4460040A146 /* FirebaseStorage */,
				CBF8EB812C62D9DB00DD96DA /* ContentfulRichTextRenderer */,
				CB99E0442C62DE6700FC8F16 /* RiveRuntime */,
				CB7DA78D2C637260000952A5 /* Alamofire */,
				CBDBEA7A2C6A99EC008EF17F /* GoogleSignIn */,
				CBDBEA7C2C6A99EC008EF17F /* GoogleSignInSwift */,
				CBF373442C6ACBDE00A348C2 /* SwiftfulFirebaseAuth */,
				CBF373552C6BF70200A348C2 /* RevenueCat */,
				CBF373572C6BF70200A348C2 /* RevenueCatUI */,
				CBFB0FFF2C7DE4D0009F18B4 /* MarkdownUI */,
				CBFB101B2C81BD34009F18B4 /* FirebaseAnalytics */,
				CBFB101D2C81BD34009F18B4 /* FirebaseMessaging */,
				CB0FFE812C91DE5100A1F243 /* FirebaseRemoteConfig */,
				CB98843F2CA5E7B800B6950C /* FirebaseFirestoreSwift */,
				CBAD51E32CBDCB8000B1BF13 /* Lottie */,
			);
			productName = tressless;
			productReference = CBD74E622C62C4A70040A146 /* tressless.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		CBD74E5A2C62C4A70040A146 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					CBD74E612C62C4A70040A146 = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = CBD74E5D2C62C4A70040A146 /* Build configuration list for PBXProject "tressless" */;
			compatibilityVersion = "Xcode 15.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = CBD74E592C62C4A70040A146;
			packageReferences = (
				CBD74EED2C62D26E0040A146 /* XCRemoteSwiftPackageReference "contentful" */,
				CBD74EF02C62D27E0040A146 /* XCRemoteSwiftPackageReference "Kingfisher" */,
				CBD74EF32C62D29C0040A146 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */,
				CBD74EF62C62D4460040A146 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				CBF8EB7D2C62D98500DD96DA /* XCRemoteSwiftPackageReference "rive-ios" */,
				CBF8EB802C62D9DB00DD96DA /* XCRemoteSwiftPackageReference "rich-text-renderer" */,
				CB7DA78C2C637260000952A5 /* XCRemoteSwiftPackageReference "Alamofire" */,
				CBDBEA792C6A99EC008EF17F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
				CBF373432C6ACBDE00A348C2 /* XCRemoteSwiftPackageReference "SwiftfulFirebaseAuth" */,
				CBF373542C6BF70200A348C2 /* XCRemoteSwiftPackageReference "purchases-ios" */,
				CBFB0FFE2C7DE4D0009F18B4 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */,
				CB2B12292CBDCAF200250578 /* XCRemoteSwiftPackageReference "lottie-ios" */,
			);
			productRefGroup = CBD74E632C62C4A70040A146 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				CBD74E612C62C4A70040A146 /* tressless */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		CBD74E602C62C4A70040A146 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CBD74EE62C62D0AD0040A146 /* Inter-SemiBold.ttf in Resources */,
				CBAD523D2CBDEBA500B1BF13 /* scanning.json in Resources */,
				CBD74EDA2C62D0A20040A146 /* check.riv in Resources */,
				CBD74E6D2C62C4A90040A146 /* Preview Assets.xcassets in Resources */,
				CBAD523F2CBDF22000B1BF13 /* scan.json in Resources */,
				CBD74EE72C62D0AD0040A146 /* KdamThmorPro-Regular.ttf in Resources */,
				CB2B12262CBDCA0300250578 /* follicle.json in Resources */,
				CB0FFEAE2C9570BC00A1F243 /* Assets.xcassets in Resources */,
				CBF3735A2C6C070800A348C2 /* Tressless.storekit in Resources */,
				CBD74ED92C62D0A20040A146 /* button.riv in Resources */,
				CBD74EDD2C62D0A20040A146 /* icons2.riv in Resources */,
				CBD74EDF2C62D0A20040A146 /* shapes.riv in Resources */,
				CB0FFE7A2C9193B200A1F243 /* icons.riv in Resources */,
				CBD74EDC2C62D0A20040A146 /* house.riv in Resources */,
				CBD74EE82C62D0AD0040A146 /* Poppins-Bold.ttf in Resources */,
				CBD74EE52C62D0AD0040A146 /* Inter-Regular.ttf in Resources */,
				CBD74EDE2C62D0A20040A146 /* menu_button.riv in Resources */,
				CBF373532C6ACF5600A348C2 /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		CBD74E5E2C62C4A70040A146 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CBD74EC42C62CFFA0040A146 /* Course.swift in Sources */,
				CBD74EAF2C62CE650040A146 /* ResultsView.swift in Sources */,
				CBD74EA92C62CD370040A146 /* AddReminderView2.swift in Sources */,
				CBD74ED02C62D07D0040A146 /* Classify.swift in Sources */,
				CBD74E7B2C62C58B0040A146 /* MedicationManager.swift in Sources */,
				CBF373812C75465600A348C2 /* ToolsView.swift in Sources */,
				CBD74E862C62C6A70040A146 /* TabBar.swift in Sources */,
				CB0FFE882C95697000A1F243 /* TabbarView.swift in Sources */,
				CBF3737B2C75274A00A348C2 /* AgeSelectionView.swift in Sources */,
				CBD74EB42C62CEBC0040A146 /* HCard.swift in Sources */,
				CBD74E662C62C4A70040A146 /* tresslessApp.swift in Sources */,
				************************ /* PastAnalysis.swift in Sources */,
				CBD74E782C62C53C0040A146 /* String.swift in Sources */,
				4187755A2DE8144C0017380D /* NotificationService.swift in Sources */,
				CBD74EA32C62CA690040A146 /* GeminiView.swift in Sources */,
				CB0FFE8C2C9569C000A1F243 /* CreateAccountView.swift in Sources */,
				65081D4F2DFC2CA4001256DE /* FirstNoticeChangesView.swift in Sources */,
				CBD74ECA2C62D0320040A146 /* MedicationDocument.swift in Sources */,
				CBD74E9D2C62C99B0040A146 /* HomeView.swift in Sources */,
				CBFB10282C86660B009F18B4 /* CustomizingView.swift in Sources */,
				CBD74E742C62C4E70040A146 /* ImageService.swift in Sources */,
				CBD74EA52C62CA8E0040A146 /* ReminderView.swift in Sources */,
				CBD74EC62C62D00F0040A146 /* CourseSection.swift in Sources */,
				CBD74EB12C62CE8B0040A146 /* ImagePicker.swift in Sources */,
				CBD74EC82C62D0210040A146 /* MedicationItem.swift in Sources */,
				************************ /* GIFBackgroundView.swift in Sources */,
				CBD74EB92C62CF280040A146 /* Extensions.swift in Sources */,
				CB0FFE8A2C95699C00A1F243 /* RootView.swift in Sources */,
				6552D0AF2DC1190D004CDC0F /* FamilyHistoryView.swift in Sources */,
				65F0B6F92DDBB5BF0046BC6B /* ProgressGraphView.swift in Sources */,
				CB0FFE802C91DE3300A1F243 /* RemoteConfigManager.swift in Sources */,
				CBD74EA72C62CC140040A146 /* PathView.swift in Sources */,
				CBD74E9F2C62C9BC0040A146 /* OnboardView.swift in Sources */,
				CBD74EFE2C62D4FF0040A146 /* ContentView.swift in Sources */,
				CB0FFE842C95170300A1F243 /* AppAlert.swift in Sources */,
				CBD74E8C2C62C7510040A146 /* BlogHome.swift in Sources */,
				CBF373742C7135A100A348C2 /* ReviewView.swift in Sources */,
				************************ /* PastAnalysesView.swift in Sources */,
				CBFB10262C865B47009F18B4 /* CustomAlertView.swift in Sources */,
				CBF373612C6C153400A348C2 /* PayView.swift in Sources */,
				CBD74EB62C62CEDC0040A146 /* VCard.swift in Sources */,
				CBD74EA12C62C9F20040A146 /* ProgressView.swift in Sources */,
				CBD74EBD2C62CF630040A146 /* CustomButton.swift in Sources */,
				CBF3737D2C75292600A348C2 /* GoalsView.swift in Sources */,
				CBD1FFCF2D402A67008D5602 /* LockedResultsView.swift in Sources */,
				CBFB0FFD2C7DDFC2009F18B4 /* BlogService.swift in Sources */,
				CB2B12282CBDCA4200250578 /* LottieView.swift in Sources */,
				CBD74ECC2C62D0440040A146 /* BlogPost.swift in Sources */,
				CBD74EC12C62CFB20040A146 /* Colors.swift in Sources */,
				65081D512DFC2EA5001256DE /* FeelHairSituationView.swift in Sources */,
				CB1A3C3E2C986F33002DB3C6 /* DeepLinkHandler.swift in Sources */,
				CBFB100D2C807B8F009F18B4 /* ProfileView.swift in Sources */,
				CBFB10032C7E816B009F18B4 /* UserGuidingView.swift in Sources */,
				CBF373712C71313100A348C2 /* SelectionButton.swift in Sources */,
				CBD74EAD2C62CE480040A146 /* ImageGuideView.swift in Sources */,
				CBD74E7D2C62C5A50040A146 /* LocalFileManager.swift in Sources */,
				6552D0AD2DC116B9004CDC0F /* ScalpConditionView.swift in Sources */,
				CBD74EAB2C62CE2E0040A146 /* ClassifyView.swift in Sources */,
				CB0FFE862C9568DA00A1F243 /* AsButtonViewModifier.swift in Sources */,
				CBFB10202C84FDCE009F18B4 /* WelcomeView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		CBD74E6E2C62C4A90040A146 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 8UQJ48K8KH;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		CBD74E6F2C62C4A90040A146 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 8UQJ48K8KH;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		CBD74E712C62C4A90040A146 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon3;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CODE_SIGN_ENTITLEMENTS = tressless/tressless.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"tressless/Preview Content\"";
				DEVELOPMENT_TEAM = 8UQJ48K8KH;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = tressless/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Regrow Hair";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.38;
				PRODUCT_BUNDLE_IDENTIFIER = LazuriteX.tressless;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		CBD74E722C62C4A90040A146 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon3;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CODE_SIGN_ENTITLEMENTS = tressless/tressless.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"tressless/Preview Content\"";
				DEVELOPMENT_TEAM = 8UQJ48K8KH;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = tressless/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Regrow Hair";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.38;
				PRODUCT_BUNDLE_IDENTIFIER = LazuriteX.tressless;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		CBD74E5D2C62C4A70040A146 /* Build configuration list for PBXProject "tressless" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CBD74E6E2C62C4A90040A146 /* Debug */,
				CBD74E6F2C62C4A90040A146 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CBD74E702C62C4A90040A146 /* Build configuration list for PBXNativeTarget "tressless" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CBD74E712C62C4A90040A146 /* Debug */,
				CBD74E722C62C4A90040A146 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		CB2B12292CBDCAF200250578 /* XCRemoteSwiftPackageReference "lottie-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/lottie-ios";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.5.0;
			};
		};
		CB7DA78C2C637260000952A5 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.9.1;
			};
		};
		CBD74EED2C62D26E0040A146 /* XCRemoteSwiftPackageReference "contentful" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/contentful/contentful.swift/";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.5.11;
			};
		};
		CBD74EF02C62D27E0040A146 /* XCRemoteSwiftPackageReference "Kingfisher" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/onevcat/Kingfisher.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 7.12.0;
			};
		};
		CBD74EF32C62D29C0040A146 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageSwiftUI";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.1.1;
			};
		};
		CBD74EF62C62D4460040A146 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 10.25.0;
			};
		};
		CBDBEA792C6A99EC008EF17F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 7.1.0;
			};
		};
		CBF373432C6ACBDE00A348C2 /* XCRemoteSwiftPackageReference "SwiftfulFirebaseAuth" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwiftfulThinking/SwiftfulFirebaseAuth.git";
			requirement = {
				kind = exactVersion;
				version = 1.0.6;
			};
		};
		CBF373542C6BF70200A348C2 /* XCRemoteSwiftPackageReference "purchases-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/RevenueCat/purchases-ios.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.27.1;
			};
		};
		CBF8EB7D2C62D98500DD96DA /* XCRemoteSwiftPackageReference "rive-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/rive-app/rive-ios";
			requirement = {
				kind = exactVersion;
				version = 2.0.20;
			};
		};
		CBF8EB802C62D9DB00DD96DA /* XCRemoteSwiftPackageReference "rich-text-renderer" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/contentful/rich-text-renderer.swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.4.2;
			};
		};
		CBFB0FFE2C7DE4D0009F18B4 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/gonzalezreal/swift-markdown-ui";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		CB0FFE812C91DE5100A1F243 /* FirebaseRemoteConfig */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBD74EF62C62D4460040A146 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseRemoteConfig;
		};
		CB7DA78D2C637260000952A5 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = CB7DA78C2C637260000952A5 /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		CB98843F2CA5E7B800B6950C /* FirebaseFirestoreSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBD74EF62C62D4460040A146 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseFirestoreSwift;
		};
		CB99E0442C62DE6700FC8F16 /* RiveRuntime */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBF8EB7D2C62D98500DD96DA /* XCRemoteSwiftPackageReference "rive-ios" */;
			productName = RiveRuntime;
		};
		CBAD51E32CBDCB8000B1BF13 /* Lottie */ = {
			isa = XCSwiftPackageProductDependency;
			package = CB2B12292CBDCAF200250578 /* XCRemoteSwiftPackageReference "lottie-ios" */;
			productName = Lottie;
		};
		CBD74EEE2C62D26E0040A146 /* Contentful */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBD74EED2C62D26E0040A146 /* XCRemoteSwiftPackageReference "contentful" */;
			productName = Contentful;
		};
		CBD74EF12C62D27E0040A146 /* Kingfisher */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBD74EF02C62D27E0040A146 /* XCRemoteSwiftPackageReference "Kingfisher" */;
			productName = Kingfisher;
		};
		CBD74EF42C62D29C0040A146 /* SDWebImageSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBD74EF32C62D29C0040A146 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */;
			productName = SDWebImageSwiftUI;
		};
		CBD74EF72C62D4460040A146 /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBD74EF62C62D4460040A146 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
		CBD74EF92C62D4460040A146 /* FirebaseFirestore */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBD74EF62C62D4460040A146 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseFirestore;
		};
		CBD74EFB2C62D4460040A146 /* FirebaseStorage */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBD74EF62C62D4460040A146 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseStorage;
		};
		CBDBEA7A2C6A99EC008EF17F /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBDBEA792C6A99EC008EF17F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		CBDBEA7C2C6A99EC008EF17F /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBDBEA792C6A99EC008EF17F /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
		CBF373442C6ACBDE00A348C2 /* SwiftfulFirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBF373432C6ACBDE00A348C2 /* XCRemoteSwiftPackageReference "SwiftfulFirebaseAuth" */;
			productName = SwiftfulFirebaseAuth;
		};
		CBF373552C6BF70200A348C2 /* RevenueCat */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBF373542C6BF70200A348C2 /* XCRemoteSwiftPackageReference "purchases-ios" */;
			productName = RevenueCat;
		};
		CBF373572C6BF70200A348C2 /* RevenueCatUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBF373542C6BF70200A348C2 /* XCRemoteSwiftPackageReference "purchases-ios" */;
			productName = RevenueCatUI;
		};
		CBF8EB812C62D9DB00DD96DA /* ContentfulRichTextRenderer */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBF8EB802C62D9DB00DD96DA /* XCRemoteSwiftPackageReference "rich-text-renderer" */;
			productName = ContentfulRichTextRenderer;
		};
		CBFB0FFF2C7DE4D0009F18B4 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBFB0FFE2C7DE4D0009F18B4 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */;
			productName = MarkdownUI;
		};
		CBFB101B2C81BD34009F18B4 /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBD74EF62C62D4460040A146 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		CBFB101D2C81BD34009F18B4 /* FirebaseMessaging */ = {
			isa = XCSwiftPackageProductDependency;
			package = CBD74EF62C62D4460040A146 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseMessaging;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = CBD74E5A2C62C4A70040A146 /* Project object */;
}
