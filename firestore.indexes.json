{"indexes": [{"collectionGroup": "scheduledNotifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "scheduledFor", "order": "ASCENDING"}]}, {"collectionGroup": "scheduledNotifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "medicationId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "scheduledNotifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "scheduledNotifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "fcmToken", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "fcmToken", "order": "ASCENDING"}, {"fieldPath": "lastActivityAt", "order": "ASCENDING"}]}, {"collectionGroup": "scheduledNotifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "medicationId", "order": "ASCENDING"}, {"fieldPath": "scheduledDate", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}], "fieldOverrides": []}