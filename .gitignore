# Xcode user-specific files (existing + improved)
tressless.xcodeproj/project.xcworkspace/xcuserdata/
tressless.xcodeproj/xcuserdata/
*.xcuserstate

# IDE files
.idea/
.vscode/

# Testing files
test_notification.apns
*.apns

# Firebase Functions
functions/node_modules/
functions/.env
functions/firebase-debug.log
functions/firebase-debug.*.log
functions/package-lock.json

# macOS
.DS_Store

# Logs
*.logfunctions/package-lock.json
