rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Users can only read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // All user subcollections - simple ownership rule
      match /{subcollection=**} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }

    // Anonymous FCM tokens - allow both authenticated and unauthenticated users to manage tokens
    match /anonymousTokens/{tokenId} {
      allow read, write: if true; // Allow all users to manage anonymous tokens (they're identified by token ID)
    }

    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}